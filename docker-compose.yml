services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: inspirflow-backend
    env_file:
      - .env
    ports:
      - "${PORT:-20010}:${PORT:-20010}"
    environment:
      # 应用配置
      - APP_NAME=${APP_NAME}
      - APP_VERSION=${APP_VERSION}
      - ENVIRONMENT=${ENVIRONMENT}
      - DEBUG=${DEBUG}
      - HOST=${HOST}
      - PORT=${PORT}
      
      # JWT配置
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM}
      - JWT_EXPIRE_HOURS=${JWT_EXPIRE_HOURS}
      
      # 外部API配置
      - MODEL_API_BASE_URL=${MODEL_API_BASE_URL}
      - MODEL_API_KEY=${MODEL_API_KEY}
      - RECORD_API_BASE_URL=${RECORD_API_BASE_URL}
      - RECORD_API_KEY=${RECORD_API_KEY}
      
      # MinIO配置
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET=${MINIO_BUCKET}
      - MINIO_SECURE=${MINIO_SECURE}
      
      # CORS配置 - 使用代码中的默认值
      
      # 聊天配置
      - DEFAULT_MODEL=${DEFAULT_MODEL}
      - DEFAULT_TEMPERATURE=${DEFAULT_TEMPERATURE}
      - MAX_TOKENS=${MAX_TOKENS}
      
      # 文件上传配置
      - MAX_FILE_SIZE=${MAX_FILE_SIZE}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL}
      - LOG_FORMAT=${LOG_FORMAT}
    volumes:
      - ./backend/static:/app/static
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-20010}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - inspirflow-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_PROXY_TARGET=${VITE_PROXY_TARGET}
        - VITE_WS_URL=${VITE_WS_URL}
        - VITE_DEV_MODE=${VITE_DEV_MODE}
        - VITE_DEBUG_LOG=${VITE_DEBUG_LOG}
        - VITE_ENABLE_LATEX=${VITE_ENABLE_LATEX}
        - VITE_ENABLE_DARK_THEME=${VITE_ENABLE_DARK_THEME}
        - VITE_DEFAULT_THEME=${VITE_DEFAULT_THEME}
        - VITE_DEFAULT_LANGUAGE=${VITE_DEFAULT_LANGUAGE}
        - VITE_SIDEBAR_COLLAPSED=${VITE_SIDEBAR_COLLAPSED}
        - VITE_DEFAULT_MODEL=${VITE_DEFAULT_MODEL}
        - VITE_DEFAULT_TEMPERATURE=${VITE_DEFAULT_TEMPERATURE}
        - VITE_MAX_MESSAGE_LENGTH=${VITE_MAX_MESSAGE_LENGTH}
        - VITE_MESSAGE_HISTORY_LIMIT=${VITE_MESSAGE_HISTORY_LIMIT}
        - VITE_MAX_FILE_SIZE=${VITE_MAX_FILE_SIZE}
        - VITE_ALLOWED_IMAGE_TYPES=${VITE_ALLOWED_IMAGE_TYPES}
        - VITE_REQUEST_TIMEOUT=${VITE_REQUEST_TIMEOUT}
        - VITE_AUTO_SAVE_INTERVAL=${VITE_AUTO_SAVE_INTERVAL}
    container_name: inspirflow-frontend
    env_file:
      - .env
    ports:
      - "${FRONTEND_PORT:-20020}:80"
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - inspirflow-network

networks:
  inspirflow-network:
    driver: bridge

volumes:
  logs:
    driver: local
