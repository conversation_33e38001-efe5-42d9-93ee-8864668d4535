# ===== 敏感信息文件 =====
.env.development
*.key
*.pem
*.p12
*.pfx

# ===== Python =====
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===== Virtual Environment =====
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# ===== PyCharm =====
.idea/

# ===== VS Code =====
.vscode/

# ===== Jupyter Notebook =====
.ipynb_checkpoints

# ===== pytest =====
.pytest_cache/
.coverage
htmlcov/

# ===== mypy =====
.mypy_cache/
.dmypy.json
dmypy.json

# ===== Node.js =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===== Runtime data =====
pids
*.pid
*.seed
*.pid.lock

# ===== Coverage directory used by tools like istanbul =====
coverage/
*.lcov

# ===== nyc test coverage =====
.nyc_output

# ===== Grunt intermediate storage =====
.grunt

# ===== Bower dependency directory =====
bower_components

# ===== node-waf configuration =====
.lock-wscript

# ===== Compiled binary addons =====
build/Release

# ===== Dependency directories =====
jspm_packages/

# ===== TypeScript cache =====
*.tsbuildinfo

# ===== Optional npm cache directory =====
.npm

# ===== Optional eslint cache =====
.eslintcache

# ===== Optional stylelint cache =====
.stylelintcache

# ===== Microbundle cache =====
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===== Optional REPL history =====
.node_repl_history

# ===== Output of 'npm pack' =====
*.tgz

# ===== Yarn Integrity file =====
.yarn-integrity

# ===== parcel-bundler cache =====
.cache
.parcel-cache

# ===== Next.js build output =====
.next

# ===== Nuxt.js build / generate output =====
.nuxt
dist

# ===== Gatsby files =====
.cache/
public

# ===== Storybook build outputs =====
.out
.storybook-out
storybook-static

# ===== Temporary folders =====
tmp/
temp/

# ===== Logs =====
logs
*.log

# ===== Runtime data =====
pids
*.pid
*.seed
*.pid.lock

# ===== OS generated files =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== Docker =====
.dockerignore

# ===== Database =====
*.db
*.sqlite
*.sqlite3

# ===== Backup files =====
*.bak
*.backup
*.old

# ===== Local configuration =====
config.local.js
config.local.json
local.json
local.js
