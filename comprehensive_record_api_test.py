#!/usr/bin/env python3
"""
全面的Record API测试 - 覆盖所有端点
"""

import requests
import json
import time
from datetime import datetime
from decimal import Decimal

# API 配置
API_BASE = "http://43.155.146.157:20002"
ADMIN_API_KEY = "admin-api-key-change-in-production"
TEST_USER_KEY = "test-key-123"

class RecordAPITester:
    def __init__(self):
        self.test_results = []
        self.created_resources = {
            'users': [],
            'conversations': [],
            'messages': []
        }
    
    def log_test(self, test_name, success, details=""):
        """记录测试结果"""
        self.test_results.append({
            'name': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}: {details}")
    
    def admin_headers(self):
        """管理员请求头"""
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {ADMIN_API_KEY}'
        }
    
    def user_headers(self, api_key=TEST_USER_KEY):
        """用户请求头"""
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
    
    def test_public_endpoints(self):
        """测试公共端点"""
        print("\n🔍 测试公共端点...")
        
        # 健康检查
        try:
            response = requests.get(f"{API_BASE}/health", timeout=10)
            self.log_test("健康检查", response.status_code == 200, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("健康检查", False, f"异常: {e}")
        
        # 公共ping
        try:
            response = requests.get(f"{API_BASE}/api/public/ping", timeout=10)
            self.log_test("公共Ping", response.status_code == 200, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("公共Ping", False, f"异常: {e}")
        
        # API文档
        try:
            response = requests.get(f"{API_BASE}/docs", timeout=10)
            self.log_test("API文档", response.status_code == 200, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("API文档", False, f"异常: {e}")
    
    def test_admin_user_management(self):
        """测试管理员用户管理端点"""
        print("\n🔍 测试管理员用户管理端点...")
        
        # 获取用户列表
        try:
            response = requests.get(f"{API_BASE}/api/v1/admin/users", headers=self.admin_headers(), timeout=10)
            if response.status_code == 200:
                users = response.json()
                self.log_test("获取用户列表", True, f"获取到 {len(users)} 个用户")
            else:
                self.log_test("获取用户列表", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("获取用户列表", False, f"异常: {e}")
        
        # 创建测试用户
        test_user_key = f"test_api_user_{int(time.time())}"
        try:
            user_data = {
                "api_key": test_user_key,
                "permission": 2,
                "is_active": True,
                "mathjax": False
            }
            response = requests.post(f"{API_BASE}/api/v1/admin/users", 
                                   headers=self.admin_headers(), json=user_data, timeout=10)
            if response.status_code in [200, 201]:
                user = response.json()
                user_id = user['id']
                self.created_resources['users'].append(user_id)
                self.log_test("创建用户", True, f"用户ID: {user_id}")
                
                # 获取特定用户
                try:
                    response = requests.get(f"{API_BASE}/api/v1/admin/users/{user_id}", 
                                          headers=self.admin_headers(), timeout=10)
                    self.log_test("获取特定用户", response.status_code == 200, f"状态码: {response.status_code}")
                except Exception as e:
                    self.log_test("获取特定用户", False, f"异常: {e}")
                
                # 更新用户
                try:
                    update_data = {
                        "api_key": test_user_key,
                        "permission": 3,
                        "is_active": True,
                        "mathjax": True
                    }
                    response = requests.put(f"{API_BASE}/api/v1/admin/users/{user_id}", 
                                          headers=self.admin_headers(), json=update_data, timeout=10)
                    self.log_test("更新用户", response.status_code == 200, f"状态码: {response.status_code}")
                except Exception as e:
                    self.log_test("更新用户", False, f"异常: {e}")
                
                # 用户充值
                try:
                    recharge_data = {"amount": 5.0, "note": "API测试充值"}
                    response = requests.post(f"{API_BASE}/api/v1/admin/users/{user_id}/recharge", 
                                           headers=self.admin_headers(), json=recharge_data, timeout=10)
                    self.log_test("用户充值", response.status_code == 200, f"状态码: {response.status_code}")
                except Exception as e:
                    self.log_test("用户充值", False, f"异常: {e}")
                
                # 获取用户计费记录
                try:
                    response = requests.get(f"{API_BASE}/api/v1/admin/users/{user_id}/billing",
                                          headers=self.admin_headers(), timeout=10)
                    if response.status_code == 200:
                        billing_records = response.json()
                        self.log_test("获取计费记录", True, f"获取到 {len(billing_records)} 条记录")
                    else:
                        self.log_test("获取计费记录", False, f"状态码: {response.status_code}")
                except Exception as e:
                    self.log_test("获取计费记录", False, f"异常: {e}")

                # 获取用户充值记录
                try:
                    response = requests.get(f"{API_BASE}/api/v1/admin/users/{user_id}/recharge-records",
                                          headers=self.admin_headers(), timeout=10)
                    if response.status_code == 200:
                        recharge_records = response.json()
                        self.log_test("获取充值记录", True, f"获取到 {len(recharge_records)} 条记录")
                    else:
                        self.log_test("获取充值记录", False, f"状态码: {response.status_code}")
                except Exception as e:
                    self.log_test("获取充值记录", False, f"异常: {e}")
                
                return test_user_key
            else:
                self.log_test("创建用户", False, f"状态码: {response.status_code}")
                return None
        except Exception as e:
            self.log_test("创建用户", False, f"异常: {e}")
            return None
    
    def test_user_profile_endpoints(self, api_key=TEST_USER_KEY):
        """测试用户资料端点"""
        print("\n🔍 测试用户资料端点...")
        
        # 获取用户资料
        try:
            response = requests.get(f"{API_BASE}/api/v1/user/profile", 
                                  headers=self.user_headers(api_key), timeout=10)
            if response.status_code == 200:
                profile = response.json()
                self.log_test("获取用户资料", True, f"用户ID: {profile.get('id')}, 权限: {profile.get('permission')}")
            else:
                self.log_test("获取用户资料", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("获取用户资料", False, f"异常: {e}")
        
        # 更新用户资料
        try:
            update_data = {"mathjax": True}
            response = requests.put(f"{API_BASE}/api/v1/user/profile", 
                                  headers=self.user_headers(api_key), json=update_data, timeout=10)
            self.log_test("更新用户资料", response.status_code == 200, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("更新用户资料", False, f"异常: {e}")
        
        # 获取用户计费信息
        try:
            response = requests.get(f"{API_BASE}/api/v1/user/billing",
                                  headers=self.user_headers(api_key), timeout=10)
            if response.status_code == 200:
                billing_records = response.json()
                self.log_test("获取计费信息", True, f"获取到 {len(billing_records)} 条记录")
            else:
                self.log_test("获取计费信息", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("获取计费信息", False, f"异常: {e}")
    
    def test_conversation_endpoints(self, api_key=TEST_USER_KEY):
        """测试对话管理端点"""
        print("\n🔍 测试对话管理端点...")
        
        # 创建对话
        conversation_id = None
        try:
            conversation_data = {"title": f"API测试对话 {datetime.now().strftime('%H:%M:%S')}"}
            response = requests.post(f"{API_BASE}/api/v1/user/conversations/", 
                                   headers=self.user_headers(api_key), json=conversation_data, timeout=10)
            if response.status_code in [200, 201]:
                conversation = response.json()
                conversation_id = conversation['id']
                self.created_resources['conversations'].append(conversation_id)
                self.log_test("创建对话", True, f"对话ID: {conversation_id}")
            else:
                self.log_test("创建对话", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("创建对话", False, f"异常: {e}")
        
        if conversation_id:
            # 获取对话列表
            try:
                response = requests.get(f"{API_BASE}/api/v1/user/conversations/", 
                                      headers=self.user_headers(api_key), timeout=10)
                if response.status_code == 200:
                    conversations = response.json()
                    self.log_test("获取对话列表", True, f"获取到 {len(conversations)} 个对话")
                else:
                    self.log_test("获取对话列表", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test("获取对话列表", False, f"异常: {e}")
            
            # 获取特定对话
            try:
                response = requests.get(f"{API_BASE}/api/v1/user/conversations/{conversation_id}", 
                                      headers=self.user_headers(api_key), timeout=10)
                self.log_test("获取特定对话", response.status_code == 200, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test("获取特定对话", False, f"异常: {e}")
            
            # 更新对话
            try:
                update_data = {"title": f"更新的API测试对话 {datetime.now().strftime('%H:%M:%S')}"}
                response = requests.put(f"{API_BASE}/api/v1/user/conversations/{conversation_id}", 
                                      headers=self.user_headers(api_key), json=update_data, timeout=10)
                self.log_test("更新对话", response.status_code == 200, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test("更新对话", False, f"异常: {e}")
        
        return conversation_id

    def test_message_endpoints(self, conversation_id, api_key=TEST_USER_KEY):
        """测试消息管理端点"""
        print("\n🔍 测试消息管理端点...")

        if not conversation_id:
            self.log_test("消息测试", False, "没有可用的对话ID")
            return

        # 创建用户消息
        user_message_id = None
        try:
            message_data = {
                "conversation_id": conversation_id,
                "role": "user",
                "content_url": "https://example.com/user_message.txt"
            }
            response = requests.post(f"{API_BASE}/api/v1/user/messages/",
                                   headers=self.user_headers(api_key), json=message_data, timeout=10)
            if response.status_code in [200, 201]:
                message = response.json()
                user_message_id = message['id']
                self.created_resources['messages'].append(user_message_id)
                self.log_test("创建用户消息", True, f"消息ID: {user_message_id}")
            else:
                self.log_test("创建用户消息", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("创建用户消息", False, f"异常: {e}")

        # 创建助手消息
        assistant_message_id = None
        try:
            message_data = {
                "conversation_id": conversation_id,
                "role": "assistant",
                "content_url": "https://example.com/assistant_message.txt",
                "model_id": 1,
                "temperature": "0.7",
                "max_tokens": 1000
            }
            response = requests.post(f"{API_BASE}/api/v1/user/messages/",
                                   headers=self.user_headers(api_key), json=message_data, timeout=10)
            if response.status_code in [200, 201]:
                message = response.json()
                assistant_message_id = message['id']
                self.created_resources['messages'].append(assistant_message_id)
                self.log_test("创建助手消息", True, f"消息ID: {assistant_message_id}")
            else:
                self.log_test("创建助手消息", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("创建助手消息", False, f"异常: {e}")

        # 获取消息列表
        try:
            response = requests.get(f"{API_BASE}/api/v1/user/messages/?conversation_id={conversation_id}",
                                  headers=self.user_headers(api_key), timeout=10)
            if response.status_code == 200:
                messages = response.json()
                self.log_test("获取消息列表", True, f"获取到 {len(messages)} 条消息")
            else:
                self.log_test("获取消息列表", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("获取消息列表", False, f"异常: {e}")

        # 获取特定消息
        if user_message_id:
            try:
                response = requests.get(f"{API_BASE}/api/v1/user/messages/{user_message_id}",
                                      headers=self.user_headers(api_key), timeout=10)
                self.log_test("获取特定消息", response.status_code == 200, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test("获取特定消息", False, f"异常: {e}")

            # 更新消息
            try:
                update_data = {"affiliated_url": "https://example.com/updated_content.txt"}
                response = requests.put(f"{API_BASE}/api/v1/user/messages/{user_message_id}",
                                      headers=self.user_headers(api_key), json=update_data, timeout=10)
                self.log_test("更新消息", response.status_code == 200, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test("更新消息", False, f"异常: {e}")

            # 测试消息计费记录创建
            try:
                billing_data = {
                    "user_id": 1,  # 假设用户ID为1
                    "message_id": user_message_id,
                    "conversation_id": conversation_id,
                    "billing_type": "message",
                    "model_name": "gpt-3.5-turbo",
                    "prompt_tokens": 100,
                    "completion_tokens": 50,
                    "prompt_cost": 0.0001,
                    "completion_cost": 0.0001,
                    "total_cost": 0.0002,
                    "description": "API测试计费"
                }
                response = requests.post(f"{API_BASE}/api/v1/user/messages/{user_message_id}/billing",
                                       headers=self.user_headers(api_key), json=billing_data, timeout=10)
                if response.status_code in [200, 201]:
                    billing_record = response.json()
                    self.log_test("创建消息计费记录", True, f"计费记录ID: {billing_record.get('id')}")
                else:
                    self.log_test("创建消息计费记录", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test("创建消息计费记录", False, f"异常: {e}")

    def test_error_handling(self):
        """测试错误处理"""
        print("\n🔍 测试错误处理...")

        # 无效的认证
        try:
            headers = {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}
            response = requests.get(f"{API_BASE}/api/v1/user/profile", headers=headers, timeout=10)
            self.log_test("无效认证处理", response.status_code == 401, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("无效认证处理", False, f"异常: {e}")

        # 权限不足
        try:
            headers = self.user_headers(TEST_USER_KEY)
            response = requests.get(f"{API_BASE}/api/v1/admin/users", headers=headers, timeout=10)
            self.log_test("权限不足处理", response.status_code == 403, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("权限不足处理", False, f"异常: {e}")

        # 资源不存在
        try:
            response = requests.get(f"{API_BASE}/api/v1/user/conversations/99999",
                                  headers=self.user_headers(), timeout=10)
            self.log_test("资源不存在处理", response.status_code == 404, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("资源不存在处理", False, f"异常: {e}")

        # 无效数据
        try:
            invalid_data = {"invalid_field": "invalid_value"}
            response = requests.post(f"{API_BASE}/api/v1/user/conversations/",
                                   headers=self.user_headers(), json=invalid_data, timeout=10)
            self.log_test("无效数据处理", response.status_code == 422, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("无效数据处理", False, f"异常: {e}")

    def test_pagination_and_filtering(self):
        """测试分页和过滤"""
        print("\n🔍 测试分页和过滤...")

        # 测试用户列表分页
        try:
            response = requests.get(f"{API_BASE}/api/v1/admin/users?page=1&size=5",
                                  headers=self.admin_headers(), timeout=10)
            if response.status_code == 200:
                users = response.json()
                self.log_test("用户列表分页", True, f"获取到 {len(users)} 个用户 (限制5个)")
            else:
                self.log_test("用户列表分页", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("用户列表分页", False, f"异常: {e}")

        # 测试计费记录分页
        try:
            response = requests.get(f"{API_BASE}/api/v1/admin/users/1/billing?page=1&size=10",
                                  headers=self.admin_headers(), timeout=10)
            self.log_test("计费记录分页", response.status_code == 200, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("计费记录分页", False, f"异常: {e}")

        # 测试充值记录查询（不再分页）
        try:
            response = requests.get(f"{API_BASE}/api/v1/admin/users/1/recharge-records",
                                  headers=self.admin_headers(), timeout=10)
            if response.status_code == 200:
                records = response.json()
                self.log_test("充值记录查询", True, f"获取到 {len(records)} 条记录")
            else:
                self.log_test("充值记录查询", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("充值记录查询", False, f"异常: {e}")

        # 测试计费记录查询（不再分页）
        try:
            response = requests.get(f"{API_BASE}/api/v1/admin/users/1/billing",
                                  headers=self.admin_headers(), timeout=10)
            if response.status_code == 200:
                records = response.json()
                self.log_test("计费记录查询", True, f"获取到 {len(records)} 条记录")
            else:
                self.log_test("计费记录查询", False, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_test("计费记录查询", False, f"异常: {e}")

    def cleanup_resources(self):
        """清理测试资源"""
        print("\n🧹 清理测试资源...")

        # 删除测试消息 (通过删除对话自动清理)

        # 删除测试对话
        for conversation_id in self.created_resources['conversations']:
            try:
                response = requests.delete(f"{API_BASE}/api/v1/user/conversations/{conversation_id}",
                                         headers=self.user_headers(), timeout=10)
                if response.status_code == 200:
                    self.log_test(f"删除对话 {conversation_id}", True, "成功")
                else:
                    self.log_test(f"删除对话 {conversation_id}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test(f"删除对话 {conversation_id}", False, f"异常: {e}")

        # 删除测试用户
        for user_id in self.created_resources['users']:
            try:
                response = requests.delete(f"{API_BASE}/api/v1/admin/users/{user_id}",
                                         headers=self.admin_headers(), timeout=10)
                if response.status_code == 200:
                    self.log_test(f"删除用户 {user_id}", True, "成功")
                else:
                    self.log_test(f"删除用户 {user_id}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test(f"删除用户 {user_id}", False, f"异常: {e}")

    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 Record API 全面测试报告")
        print("=" * 80)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for test in self.test_results if test['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {success_rate:.1f}%")

        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for test in self.test_results:
                if not test['success']:
                    print(f"  • {test['name']}: {test['details']}")

        print(f"\n📋 测试覆盖的端点:")
        endpoints_tested = [
            "✅ 公共端点 (健康检查、Ping、文档)",
            "✅ 管理员用户管理 (CRUD、充值、查询)",
            "✅ 用户资料管理 (获取、更新、计费)",
            "✅ 对话管理 (CRUD)",
            "✅ 消息管理 (CRUD)",
            "✅ 错误处理 (401、403、404、422)",
            "✅ 分页和过滤功能"
        ]

        for endpoint in endpoints_tested:
            print(f"  {endpoint}")

        if success_rate >= 90:
            print(f"\n🎉 Record API 功能完全正常！")
        elif success_rate >= 70:
            print(f"\n⚠️ Record API 大部分功能正常，有少量问题需要修复")
        else:
            print(f"\n❌ Record API 存在较多问题，需要重点检查")

        return success_rate

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Record API全面测试")
        print("=" * 80)

        # 1. 测试公共端点
        self.test_public_endpoints()

        # 2. 测试管理员功能
        test_user_key = self.test_admin_user_management()

        # 3. 测试用户资料功能
        self.test_user_profile_endpoints(test_user_key or TEST_USER_KEY)

        # 4. 测试对话功能
        conversation_id = self.test_conversation_endpoints(test_user_key or TEST_USER_KEY)

        # 5. 测试消息功能
        self.test_message_endpoints(conversation_id, test_user_key or TEST_USER_KEY)

        # 6. 测试错误处理
        self.test_error_handling()

        # 7. 测试分页和过滤
        self.test_pagination_and_filtering()

        # 8. 清理资源
        self.cleanup_resources()

        # 9. 生成报告
        return self.generate_report()


def main():
    """主函数"""
    tester = RecordAPITester()
    success_rate = tester.run_all_tests()

    print(f"\n🌐 API服务地址: {API_BASE}")
    print(f"📖 API文档: {API_BASE}/docs")
    print(f"🔑 管理员Key: {ADMIN_API_KEY}")
    print(f"🔑 测试用户Key: {TEST_USER_KEY}")

    return success_rate >= 90


if __name__ == "__main__":
    main()
