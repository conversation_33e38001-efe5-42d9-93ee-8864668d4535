#!/bin/bash

# 部署总结脚本
# 显示部署状态和访问信息

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
show_header() {
    echo ""
    echo -e "${PURPLE}=================================="
    echo -e "    InspirFlow 部署状态总结"
    echo -e "==================================${NC}"
    echo ""
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    echo ""
    
    # 获取容器状态
    if command -v docker &> /dev/null && docker compose ps &> /dev/null; then
        docker compose ps
        echo ""
        
        # 检查具体服务健康状态
        local backend_healthy=false
        local frontend_healthy=false
        
        # 检查后端健康状态
        if curl -f -s "http://localhost:20010/health" > /dev/null 2>&1; then
            backend_healthy=true
            log_success "后端服务运行正常"
        else
            log_error "后端服务不可访问"
        fi
        
        # 检查前端可访问性
        if curl -f -s "http://localhost:20020" > /dev/null 2>&1; then
            frontend_healthy=true
            log_success "前端服务运行正常"
        else
            log_error "前端服务不可访问"
        fi
        
        echo ""
        
        if $backend_healthy && $frontend_healthy; then
            log_success "🎉 所有服务运行正常！"
        else
            log_warning "⚠️  部分服务存在问题，请检查日志"
        fi
    else
        log_error "Docker 或 Docker Compose 不可用"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    echo -e "${CYAN}=================================="
    echo -e "         访问信息"
    echo -e "==================================${NC}"
    echo ""
    
    # 从环境变量获取端口
    if [ -f ".env" ]; then
        source .env
    fi
    
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    echo -e "${GREEN}🌐 应用访问地址:${NC}"
    echo -e "  📱 前端应用:    ${YELLOW}http://localhost:$frontend_port${NC}"
    echo -e "  🔧 后端 API:    ${YELLOW}http://localhost:$backend_port${NC}"
    echo -e "  ❤️  健康检查:    ${YELLOW}http://localhost:$backend_port/health${NC}"
    echo -e "  📚 API 文档:    ${YELLOW}http://localhost:$backend_port/docs${NC}"
    echo ""
    
    echo -e "${GREEN}🔗 外部访问地址:${NC}"
    echo -e "  🌍 前端应用:    ${YELLOW}http://**************:$frontend_port${NC}"
    echo -e "  🌍 后端 API:    ${YELLOW}http://**************:$backend_port${NC}"
    echo ""
}

# 显示管理命令
show_management_commands() {
    echo -e "${CYAN}=================================="
    echo -e "         管理命令"
    echo -e "==================================${NC}"
    echo ""
    
    echo -e "${GREEN}📋 常用管理命令:${NC}"
    echo -e "  📊 查看状态:    ${YELLOW}docker compose ps${NC}"
    echo -e "  📝 查看日志:    ${YELLOW}docker compose logs -f${NC}"
    echo -e "  📝 后端日志:    ${YELLOW}docker compose logs -f backend${NC}"
    echo -e "  📝 前端日志:    ${YELLOW}docker compose logs -f frontend${NC}"
    echo -e "  🔄 重启服务:    ${YELLOW}docker compose restart${NC}"
    echo -e "  ⏹️  停止服务:    ${YELLOW}docker compose down${NC}"
    echo -e "  🔧 重新部署:    ${YELLOW}./one-click-deploy.sh${NC}"
    echo ""
    
    echo -e "${GREEN}🛠️  开发命令:${NC}"
    echo -e "  🏗️  重新构建:    ${YELLOW}docker compose build${NC}"
    echo -e "  🔄 强制重建:    ${YELLOW}docker compose build --no-cache${NC}"
    echo -e "  📦 前端构建:    ${YELLOW}./build-frontend.sh${NC}"
    echo -e "  🧪 环境测试:    ${YELLOW}./test-deployment.sh${NC}"
    echo ""
}

# 显示系统信息
show_system_info() {
    echo -e "${CYAN}=================================="
    echo -e "         系统信息"
    echo -e "==================================${NC}"
    echo ""
    
    # Docker 信息
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        echo -e "${GREEN}🐳 Docker:${NC} $docker_version"
        
        if docker compose version &> /dev/null; then
            local compose_version=$(docker compose version --short)
            echo -e "${GREEN}🔧 Docker Compose:${NC} v$compose_version"
        fi
    fi
    
    # Node.js 信息
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        echo -e "${GREEN}🟢 Node.js:${NC} $node_version"
    fi
    
    # 系统信息
    echo -e "${GREEN}💻 系统:${NC} $(uname -s) $(uname -m)"
    echo -e "${GREEN}⏰ 时间:${NC} $(date)"
    echo ""
}

# 显示故障排除信息
show_troubleshooting() {
    echo -e "${CYAN}=================================="
    echo -e "         故障排除"
    echo -e "==================================${NC}"
    echo ""
    
    echo -e "${GREEN}🔍 常见问题解决:${NC}"
    echo ""
    echo -e "${YELLOW}1. 服务无法启动:${NC}"
    echo "   - 检查端口占用: lsof -i :20010 -i :20020"
    echo "   - 查看详细日志: docker compose logs [service_name]"
    echo "   - 重新构建镜像: docker compose build --no-cache"
    echo ""
    
    echo -e "${YELLOW}2. 前端无法访问后端:${NC}"
    echo "   - 检查 CORS 配置"
    echo "   - 确认后端服务正常运行"
    echo "   - 检查网络连接"
    echo ""
    
    echo -e "${YELLOW}3. 容器频繁重启:${NC}"
    echo "   - 查看容器日志找出错误原因"
    echo "   - 检查环境变量配置"
    echo "   - 确认依赖服务可用"
    echo ""
    
    echo -e "${YELLOW}4. 性能问题:${NC}"
    echo "   - 查看资源使用: docker stats"
    echo "   - 检查日志级别设置"
    echo "   - 监控系统资源"
    echo ""
}

# 显示下一步建议
show_next_steps() {
    echo -e "${CYAN}=================================="
    echo -e "         下一步建议"
    echo -e "==================================${NC}"
    echo ""
    
    echo -e "${GREEN}🚀 建议的下一步操作:${NC}"
    echo ""
    echo "1. 🔐 更新生产环境的安全配置"
    echo "   - 修改 JWT_SECRET_KEY"
    echo "   - 配置 HTTPS"
    echo "   - 设置防火墙规则"
    echo ""
    
    echo "2. 📊 设置监控和日志"
    echo "   - 配置日志轮转"
    echo "   - 设置健康检查监控"
    echo "   - 配置告警通知"
    echo ""
    
    echo "3. 🔄 设置自动化"
    echo "   - 配置 CI/CD 流水线"
    echo "   - 设置自动备份"
    echo "   - 配置自动更新"
    echo ""
    
    echo "4. 🧪 测试验证"
    echo "   - 进行功能测试"
    echo "   - 执行性能测试"
    echo "   - 验证安全配置"
    echo ""
}

# 主函数
main() {
    show_header
    check_services
    show_access_info
    show_management_commands
    show_system_info
    show_troubleshooting
    show_next_steps
    
    echo -e "${PURPLE}=================================="
    echo -e "    部署总结完成"
    echo -e "==================================${NC}"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
