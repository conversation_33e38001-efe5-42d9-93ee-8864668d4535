#!/bin/bash

# Docker Compose v2 部署脚本
# 用于部署 InspirFlow 应用

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_prerequisites() {
    log_info "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose v2
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
        COMPOSE_VERSION=$(docker compose version --short)
        log_info "使用 Docker Compose v2: $COMPOSE_VERSION"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
        COMPOSE_VERSION=$(docker-compose version --short)
        log_warning "使用 Docker Compose v1: $COMPOSE_VERSION"
        log_warning "建议升级到 Docker Compose v2"
    else
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 服务状态
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
}

# 加载环境变量
load_env_vars() {
    log_info "加载环境变量..."
    
    if [ -f ".env" ]; then
        source .env
        log_success "已加载 .env 文件"
        
        # 显示关键配置
        log_info "关键配置信息:"
        echo "  - 后端端口: ${BACKEND_PORT:-20010}"
        echo "  - 前端端口: ${FRONTEND_PORT:-20020}"
        echo "  - 环境: ${ENVIRONMENT:-production}"
    else
        log_error "未找到 .env 文件，请先配置环境变量"
        if [ -f ".env.example" ]; then
            log_info "可以复制 .env.example 为 .env 并进行配置"
        fi
        exit 1
    fi
}

# 检查端口占用
check_ports() {
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    log_info "检查端口占用情况..."
    
    check_and_handle_port $backend_port "后端"
    check_and_handle_port $frontend_port "前端"
}

# 检查并处理单个端口
check_and_handle_port() {
    local port=$1
    local service_name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "${service_name}端口 $port 被占用"
        
        # 显示占用进程信息
        local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
        local process_info=$(ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null || echo "未知进程")
        
        echo "占用进程信息:"
        echo "  PID: $pid"
        echo "  进程: $process_info"
        
        read -p "是否终止占用端口 $port 的进程？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "正在终止进程 $pid..."
            
            # 尝试优雅终止
            if kill $pid 2>/dev/null; then
                sleep 2
                
                # 检查进程是否还在运行
                if kill -0 $pid 2>/dev/null; then
                    log_warning "优雅终止失败，强制终止进程..."
                    kill -9 $pid 2>/dev/null || true
                fi
                
                log_success "已终止占用端口 $port 的进程"
            else
                log_error "无法终止进程 $pid，可能需要管理员权限"
                exit 1
            fi
        else
            log_error "端口 $port 被占用，无法继续部署"
            exit 1
        fi
    else
        log_success "${service_name}端口 $port 可用"
    fi
}

# 构建前端（如果需要）
build_frontend_if_needed() {
    if [ ! -d "frontend/dist" ]; then
        log_warning "未找到前端构建文件，开始构建前端..."
        
        if [ -f "build-frontend.sh" ]; then
            chmod +x build-frontend.sh
            ./build-frontend.sh
        else
            log_error "未找到前端构建脚本 build-frontend.sh"
            exit 1
        fi
    else
        log_info "发现前端构建文件，跳过构建步骤"
        
        read -p "是否重新构建前端？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if [ -f "build-frontend.sh" ]; then
                chmod +x build-frontend.sh
                ./build-frontend.sh
            else
                log_error "未找到前端构建脚本 build-frontend.sh"
                exit 1
            fi
        fi
    fi
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."
    
    if $COMPOSE_CMD ps -q | grep -q .; then
        log_info "发现运行中的服务，正在停止..."
        $COMPOSE_CMD down
        log_success "已停止现有服务"
    else
        log_info "没有运行中的服务"
    fi
}

# 清理 Docker 资源（可选）
cleanup_docker_resources() {
    read -p "是否清理未使用的 Docker 资源？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理未使用的 Docker 资源..."
        
        # 清理未使用的镜像
        docker image prune -f
        
        # 清理未使用的容器
        docker container prune -f
        
        # 清理未使用的网络
        docker network prune -f
        
        log_success "Docker 资源清理完成"
    fi
}

# 部署服务
deploy_services() {
    log_info "开始部署服务..."
    
    # 构建并启动服务
    log_info "构建 Docker 镜像..."
    $COMPOSE_CMD build --no-cache
    
    log_info "启动服务..."
    $COMPOSE_CMD up -d
    
    log_success "服务部署完成"
}

# 检查服务状态
check_service_status() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 5
    
    # 显示服务状态
    $COMPOSE_CMD ps
    
    # 检查服务健康状态
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    log_info "检查服务可访问性..."
    
    # 检查后端健康状态
    if curl -f -s "http://localhost:$backend_port/health" > /dev/null; then
        log_success "后端服务健康检查通过"
    else
        log_warning "后端服务健康检查失败，请检查日志"
    fi
    
    # 检查前端可访问性
    if curl -f -s "http://localhost:$frontend_port" > /dev/null; then
        log_success "前端服务可访问"
    else
        log_warning "前端服务不可访问，请检查日志"
    fi
}

# 显示访问信息
show_access_info() {
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    echo "=================================="
    log_success "部署完成！"
    echo ""
    log_info "访问信息:"
    echo "  - 前端应用: http://localhost:$frontend_port"
    echo "  - 后端 API: http://localhost:$backend_port"
    echo "  - 后端健康检查: http://localhost:$backend_port/health"
    echo ""
    log_info "常用命令:"
    echo "  - 查看日志: $COMPOSE_CMD logs -f"
    echo "  - 停止服务: $COMPOSE_CMD down"
    echo "  - 重启服务: $COMPOSE_CMD restart"
    echo "  - 查看状态: $COMPOSE_CMD ps"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始 Docker Compose 部署流程..."
    echo "=================================="
    
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_prerequisites
    load_env_vars
    check_ports
    build_frontend_if_needed
    stop_existing_services
    cleanup_docker_resources
    deploy_services
    check_service_status
    show_access_info
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
