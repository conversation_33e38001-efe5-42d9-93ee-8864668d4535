#!/bin/bash

# InspirFlow 管理器主菜单
# 集成所有管理功能的主入口脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎标题
show_welcome() {
    clear
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗"
    echo -e "║                                                              ║"
    echo -e "║                    ${WHITE}InspirFlow 管理器${PURPLE}                      ║"
    echo -e "║                                                              ║"
    echo -e "║                   ${CYAN}智能聊天应用管理工具${PURPLE}                     ║"
    echo -e "║                                                              ║"
    echo -e "╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示系统状态
show_system_status() {
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${WHITE}                           系统状态${NC}"
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
    
    # 检查 Docker Compose
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        log_error "Docker Compose 未安装"
        return 1
    fi
    
    # 检查服务状态
    if $COMPOSE_CMD ps -q 2>/dev/null | grep -q .; then
        echo -e "${GREEN}🟢 服务状态: 运行中${NC}"
        
        # 检查具体服务
        local backend_status="❌ 离线"
        local frontend_status="❌ 离线"
        
        if curl -f -s "http://localhost:20010/health" > /dev/null 2>&1; then
            backend_status="✅ 在线"
        fi
        
        if curl -f -s "http://localhost:20020" > /dev/null 2>&1; then
            frontend_status="✅ 在线"
        fi
        
        echo -e "   📱 前端服务: $frontend_status"
        echo -e "   🔧 后端服务: $backend_status"
    else
        echo -e "${RED}🔴 服务状态: 已停止${NC}"
    fi
    
    # 显示访问地址
    echo ""
    echo -e "${YELLOW}🌐 访问地址:${NC}"
    echo -e "   前端: ${CYAN}http://localhost:20020${NC}"
    echo -e "   后端: ${CYAN}http://localhost:20010${NC}"
    echo ""
}

# 显示主菜单
show_main_menu() {
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${WHITE}                           主菜单${NC}"
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
    
    echo -e "${GREEN}🚀 部署管理${NC}"
    echo "   1) 🎯 一键部署应用"
    echo "   2) 🐳 Docker 部署"
    echo "   3) 🧪 测试部署环境"
    echo ""
    
    echo -e "${BLUE}📊 服务管理${NC}"
    echo "   4) 📋 查看部署状态"
    echo "   5) 📝 查看服务日志"
    echo "   6) ⏹️  停止所有服务"
    echo "   7) 🔄 重启服务"
    echo ""
    
    echo -e "${PURPLE}🛠️  维护工具${NC}"
    echo "   8) 💾 备份和恢复"
    echo "   9) 🧹 清理系统"
    echo "   10) 🔧 配置管理"
    echo ""
    
    echo -e "${YELLOW}📚 帮助信息${NC}"
    echo "   11) 📖 查看文档"
    echo "   12) ❓ 故障排除"
    echo "   13) ℹ️  关于系统"
    echo ""
    
    echo -e "${RED}   0) ❌ 退出${NC}"
    echo ""
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

# 执行一键部署
run_one_click_deploy() {
    echo ""
    log_info "启动一键部署..."
    echo ""
    
    if [ -f "one-click-deploy.sh" ]; then
        ./one-click-deploy.sh
    else
        log_error "一键部署脚本不存在"
    fi
}



# Docker 部署
run_docker_deploy() {
    echo ""
    log_info "启动 Docker 部署..."
    echo ""
    
    if [ -f "deploy.sh" ]; then
        ./deploy.sh
    else
        log_error "Docker 部署脚本不存在"
    fi
}

# 测试环境
run_test_environment() {
    echo ""
    log_info "启动环境测试..."
    echo ""
    
    if [ -f "test-deployment.sh" ]; then
        ./test-deployment.sh
    else
        log_error "环境测试脚本不存在"
    fi
}

# 查看部署状态
run_deployment_summary() {
    echo ""
    log_info "查看部署状态..."
    echo ""
    
    if [ -f "deployment-summary.sh" ]; then
        ./deployment-summary.sh
    else
        log_error "部署状态脚本不存在"
    fi
}

# 查看日志
run_logs_viewer() {
    echo ""
    log_info "启动日志查看器..."
    echo ""
    
    if [ -f "logs-viewer.sh" ]; then
        ./logs-viewer.sh
    else
        log_error "日志查看器脚本不存在"
    fi
}

# 停止服务
run_stop_services() {
    echo ""
    log_info "启动服务停止工具..."
    echo ""
    
    if [ -f "stop-services.sh" ]; then
        ./stop-services.sh
    else
        log_error "服务停止脚本不存在"
    fi
}

# 重启服务
run_restart_services() {
    echo ""
    log_info "重启服务..."
    echo ""
    
    if docker compose ps -q 2>/dev/null | grep -q .; then
        docker compose restart
        log_success "服务重启完成"
    else
        log_warning "没有运行中的服务"
        echo ""
        read -p "是否启动服务？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker compose up -d
            log_success "服务启动完成"
        fi
    fi
}

# 备份恢复
run_backup_restore() {
    echo ""
    log_info "启动备份恢复工具..."
    echo ""
    
    if [ -f "backup-restore.sh" ]; then
        ./backup-restore.sh
    else
        log_error "备份恢复脚本不存在"
    fi
}

# 清理系统
run_system_cleanup() {
    echo ""
    log_info "系统清理..."
    echo ""
    
    echo "可选的清理操作："
    echo "  1) 清理 Docker 容器"
    echo "  2) 清理 Docker 镜像"
    echo "  3) 清理 Docker 网络"
    echo "  4) 清理构建缓存"
    echo "  5) 全部清理"
    echo "  6) 取消"
    echo ""
    
    read -p "请选择 (1-6): " choice
    
    case $choice in
        1)
            docker container prune -f
            log_success "Docker 容器清理完成"
            ;;
        2)
            docker image prune -f
            log_success "Docker 镜像清理完成"
            ;;
        3)
            docker network prune -f
            log_success "Docker 网络清理完成"
            ;;
        4)
            docker builder prune -f
            log_success "构建缓存清理完成"
            ;;
        5)
            docker system prune -f
            log_success "系统清理完成"
            ;;
        6|*)
            log_info "清理操作已取消"
            ;;
    esac
}

# 配置管理
run_config_management() {
    echo ""
    log_info "配置管理..."
    echo ""
    
    echo "配置管理选项："
    echo "  1) 查看当前配置"
    echo "  2) 编辑环境变量"
    echo "  3) 查看 Docker Compose 配置"
    echo "  4) 验证配置"
    echo "  5) 返回主菜单"
    echo ""
    
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1)
            echo ""
            echo "当前配置："
            echo "============"
            if [ -f ".env" ]; then
                cat .env
            else
                log_error ".env 文件不存在"
            fi
            ;;
        2)
            if command -v nano &> /dev/null; then
                nano .env
            elif command -v vim &> /dev/null; then
                vim .env
            else
                log_error "没有找到可用的编辑器"
            fi
            ;;
        3)
            echo ""
            echo "Docker Compose 配置："
            echo "===================="
            cat docker-compose.yml
            ;;
        4)
            echo ""
            log_info "验证配置..."
            if [ -f ".env" ]; then
                source .env
                log_success "环境变量加载成功"
            else
                log_error ".env 文件不存在"
            fi
            ;;
        5|*)
            return 0
            ;;
    esac
}

# 查看文档
show_documentation() {
    echo ""
    log_info "查看文档..."
    echo ""
    
    echo "可用文档："
    echo "  1) 部署指南 (DEPLOYMENT_GUIDE.md)"
    echo "  2) 脚本说明 (SCRIPTS_README.md)"
    echo "  3) Docker Compose 配置"
    echo "  4) 返回主菜单"
    echo ""
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            if [ -f "docs/DEPLOYMENT_GUIDE.md" ]; then
                less docs/DEPLOYMENT_GUIDE.md
            else
                log_error "部署指南文件不存在"
            fi
            ;;
        2)
            if [ -f "docs/SCRIPTS_README.md" ]; then
                less docs/SCRIPTS_README.md
            else
                log_error "脚本说明文件不存在"
            fi
            ;;
        3)
            less docker-compose.yml
            ;;
        4|*)
            return 0
            ;;
    esac
}

# 故障排除
show_troubleshooting() {
    echo ""
    log_info "故障排除指南..."
    echo ""
    
    echo -e "${YELLOW}常见问题和解决方案：${NC}"
    echo ""
    
    echo "1. 🔴 服务无法启动"
    echo "   - 检查端口占用: lsof -i :20010 -i :20020"
    echo "   - 查看服务日志: docker compose logs"
    echo "   - 重新构建镜像: docker compose build --no-cache"
    echo ""
    
    echo "2. 🌐 前端无法访问后端"
    echo "   - 检查后端服务状态"
    echo "   - 验证 CORS 配置"
    echo "   - 检查网络连接"
    echo ""
    
    echo "3. 🐳 Docker 相关问题"
    echo "   - 检查 Docker 服务: systemctl status docker"
    echo "   - 重启 Docker: sudo systemctl restart docker"
    echo "   - 清理 Docker 资源: docker system prune"
    echo ""
    
    echo "4. 📦 构建失败"
    echo "   - 检查 Node.js 版本"
    echo "   - 清理 node_modules: rm -rf frontend/node_modules"
    echo "   - 重新安装依赖: cd frontend && npm install"
    echo ""
    
    echo "5. 🔧 配置问题"
    echo "   - 验证 .env 文件格式"
    echo "   - 检查环境变量值"
    echo "   - 确认文件权限"
    echo ""
}

# 关于系统
show_about() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗"
    echo -e "║                                                              ║"
    echo -e "║                      ${WHITE}关于 InspirFlow${PURPLE}                       ║"
    echo -e "║                                                              ║"
    echo -e "╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    echo -e "${CYAN}项目信息：${NC}"
    echo "  名称: InspirFlow"
    echo "  版本: 2.0.0"
    echo "  描述: 智能聊天应用"
    echo ""
    
    echo -e "${CYAN}技术栈：${NC}"
    echo "  前端: Vue.js + Vite + Element Plus"
    echo "  后端: FastAPI + Python"
    echo "  部署: Docker + Docker Compose"
    echo ""
    
    echo -e "${CYAN}系统信息：${NC}"
    echo "  操作系统: $(uname -s) $(uname -m)"
    echo "  Docker: $(docker --version 2>/dev/null | cut -d' ' -f3 | cut -d',' -f1 || echo '未安装')"
    echo "  Docker Compose: $(docker compose version --short 2>/dev/null || echo '未安装')"
    echo "  Node.js: $(node --version 2>/dev/null || echo '未安装')"
    echo ""
    
    echo -e "${CYAN}管理脚本：${NC}"
    echo "  一键部署: one-click-deploy.sh"
    echo "  前端构建: build-frontend.sh"
    echo "  Docker 部署: deploy.sh"
    echo "  状态查看: deployment-summary.sh"
    echo "  日志查看: logs-viewer.sh"
    echo "  服务停止: stop-services.sh"
    echo "  备份恢复: backup-restore.sh"
    echo ""
}

# 主循环
main_loop() {
    while true; do
        show_welcome
        show_system_status
        show_main_menu
        
        read -p "请选择操作 (0-13): " choice

        case $choice in
            1) run_one_click_deploy ;;
            2) run_docker_deploy ;;
            3) run_test_environment ;;
            4) run_deployment_summary ;;
            5) run_logs_viewer ;;
            6) run_stop_services ;;
            7) run_restart_services ;;
            8) run_backup_restore ;;
            9) run_system_cleanup ;;
            10) run_config_management ;;
            11) show_documentation ;;
            12) show_troubleshooting ;;
            13) show_about ;;
            0)
                echo ""
                log_info "感谢使用 InspirFlow 管理器！"
                echo ""
                exit 0
                ;;
            *)
                log_error "无效选择，请输入 0-13"
                ;;
        esac
        
        if [ "$choice" != "6" ] && [ "$choice" != "9" ] && [ "$choice" != "11" ] && [ "$choice" != "12" ]; then
            echo ""
            read -p "按 Enter 键继续..."
        fi
    done
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 进入主循环
    main_loop
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
