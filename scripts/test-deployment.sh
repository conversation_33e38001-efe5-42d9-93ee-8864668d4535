#!/bin/bash

# 部署测试脚本
# 用于测试部署脚本的功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# 测试脚本文件是否存在
test_script_files() {
    log_info "测试脚本文件..."
    
    local scripts=("build-frontend.sh" "deploy.sh" "one-click-deploy.sh")
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            log_success "脚本 $script 存在且可执行"
        else
            log_error "脚本 $script 不存在或不可执行"
            return 1
        fi
    done
}

# 测试环境变量
test_environment() {
    log_info "测试环境变量..."
    
    if [ -f ".env" ]; then
        source .env
        log_success "环境变量文件存在"
        
        if [ -n "$BACKEND_PORT" ] && [ -n "$FRONTEND_PORT" ]; then
            log_success "端口配置正确: 后端=$BACKEND_PORT, 前端=$FRONTEND_PORT"
        else
            log_error "端口配置缺失"
            return 1
        fi
    else
        log_error "环境变量文件不存在"
        return 1
    fi
}

# 测试 Docker 环境
test_docker() {
    log_info "测试 Docker 环境..."
    
    if command -v docker &> /dev/null; then
        log_success "Docker 已安装"
        
        if docker info &> /dev/null; then
            log_success "Docker 服务运行正常"
        else
            log_error "Docker 服务未运行"
            return 1
        fi
        
        if docker compose version &> /dev/null; then
            log_success "Docker Compose v2 可用"
        elif command -v docker-compose &> /dev/null; then
            log_success "Docker Compose v1 可用"
        else
            log_error "Docker Compose 不可用"
            return 1
        fi
    else
        log_error "Docker 未安装"
        return 1
    fi
}

# 测试 Node.js 环境
test_nodejs() {
    log_info "测试 Node.js 环境..."
    
    if command -v node &> /dev/null; then
        local node_version=$(node --version | cut -d'v' -f2)
        log_success "Node.js 已安装: v$node_version"
        
        if command -v npm &> /dev/null; then
            local npm_version=$(npm --version)
            log_success "npm 已安装: $npm_version"
        else
            log_error "npm 未安装"
            return 1
        fi
    else
        log_error "Node.js 未安装"
        return 1
    fi
}

# 测试前端项目结构
test_frontend_structure() {
    log_info "测试前端项目结构..."
    
    if [ -d "frontend" ]; then
        log_success "前端目录存在"
        
        if [ -f "frontend/package.json" ]; then
            log_success "package.json 存在"
        else
            log_error "package.json 不存在"
            return 1
        fi
        
        if [ -f "frontend/vite.config.js" ]; then
            log_success "vite.config.js 存在"
        else
            log_error "vite.config.js 不存在"
            return 1
        fi
    else
        log_error "前端目录不存在"
        return 1
    fi
}

# 测试端口检查功能
test_port_check() {
    log_info "测试端口检查功能..."
    
    source .env
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    # 检查端口占用
    if lsof -Pi :$backend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_info "后端端口 $backend_port 被占用（这是正常的测试情况）"
    else
        log_info "后端端口 $backend_port 可用"
    fi
    
    if lsof -Pi :$frontend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_info "前端端口 $frontend_port 被占用（这是正常的测试情况）"
    else
        log_info "前端端口 $frontend_port 可用"
    fi
    
    log_success "端口检查功能正常"
}

# 运行所有测试
run_all_tests() {
    echo "=================================="
    echo "    部署环境测试"
    echo "=================================="
    echo ""

    local tests=(
        "test_script_files"
        "test_environment"
        "test_docker"
        "test_nodejs"
        "test_frontend_structure"
        "test_port_check"
    )

    local passed=0
    local total=${#tests[@]}

    for test in "${tests[@]}"; do
        if $test; then
            ((passed++))
        else
            log_error "测试 $test 失败"
        fi
        echo ""
    done
    
    echo "=================================="
    if [ $passed -eq $total ]; then
        log_success "所有测试通过 ($passed/$total)"
        echo ""
        log_info "环境准备就绪，可以运行部署脚本："
        echo "  ./one-click-deploy.sh"
    else
        log_error "部分测试失败 ($passed/$total)"
        echo ""
        log_info "请解决上述问题后再运行部署脚本"
    fi
    echo "=================================="
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    run_all_tests
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
