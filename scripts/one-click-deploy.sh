#!/bin/bash

# 一键部署脚本
# 自动执行前端打包和 Docker Compose 部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "=================================="
    echo "    InspirFlow 一键部署脚本"
    echo "=================================="
    echo ""
    log_info "此脚本将自动执行以下步骤:"
    echo "  1. 构建前端应用"
    echo "  2. 检查并处理端口占用"
    echo "  3. 使用 Docker Compose v2 部署服务"
    echo ""
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ] || [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查必要的脚本文件
    if [ ! -f "build-frontend.sh" ]; then
        log_error "未找到前端构建脚本 build-frontend.sh"
        exit 1
    fi
    
    if [ ! -f "deploy.sh" ]; then
        log_error "未找到部署脚本 deploy.sh"
        exit 1
    fi
    
    # 确保脚本有执行权限
    chmod +x build-frontend.sh deploy.sh
    
    log_success "前置条件检查通过"
}

# 确认部署
confirm_deployment() {
    echo ""
    log_warning "准备开始部署，这将："
    echo "  - 重新构建前端应用"
    echo "  - 停止现有的 Docker 服务"
    echo "  - 重新构建 Docker 镜像"
    echo "  - 启动新的服务实例"
    echo ""
    
    read -p "确认继续部署？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 执行前端构建
build_frontend() {
    echo ""
    echo "=================================="
    log_info "步骤 1: 构建前端应用"
    echo "=================================="
    
    ./build-frontend.sh
    
    if [ $? -eq 0 ]; then
        log_success "前端构建完成"
    else
        log_error "前端构建失败"
        exit 1
    fi
}

# 执行部署
deploy_application() {
    echo ""
    echo "=================================="
    log_info "步骤 2: 部署应用"
    echo "=================================="
    
    ./deploy.sh
    
    if [ $? -eq 0 ]; then
        log_success "应用部署完成"
    else
        log_error "应用部署失败"
        exit 1
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo "=================================="
    log_success "🎉 一键部署完成！"
    echo "=================================="
    echo ""
    
    # 从环境变量获取端口信息
    if [ -f ".env" ]; then
        source .env
    fi
    
    local backend_port=${BACKEND_PORT:-20010}
    local frontend_port=${FRONTEND_PORT:-20020}
    
    log_info "🌐 访问地址:"
    echo "  📱 前端应用: http://localhost:$frontend_port"
    echo "  🔧 后端 API: http://localhost:$backend_port"
    echo "  ❤️  健康检查: http://localhost:$backend_port/health"
    echo ""
    
    log_info "📋 管理命令:"
    echo "  📊 查看状态: docker compose ps"
    echo "  📝 查看日志: docker compose logs -f"
    echo "  🔄 重启服务: docker compose restart"
    echo "  ⏹️  停止服务: docker compose down"
    echo ""
    
    log_info "🔍 故障排除:"
    echo "  如果服务无法访问，请检查:"
    echo "  1. 防火墙设置"
    echo "  2. 端口是否被其他程序占用"
    echo "  3. Docker 服务是否正常运行"
    echo "  4. 查看容器日志: docker compose logs [service_name]"
    echo ""
}

# 主函数
main() {
    show_welcome
    check_prerequisites
    confirm_deployment
    build_frontend
    deploy_application
    show_completion
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
