#!/bin/bash

# 备份和恢复脚本
# 用于备份和恢复 InspirFlow 应用数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
show_header() {
    echo ""
    echo -e "${PURPLE}=================================="
    echo -e "    InspirFlow 备份恢复工具"
    echo -e "==================================${NC}"
    echo ""
}

# 检查 Docker Compose
check_docker_compose() {
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        log_error "Docker Compose 未安装"
        exit 1
    fi
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}请选择操作：${NC}"
    echo ""
    echo "  1) 📦 创建完整备份"
    echo "  2) 💾 创建数据备份"
    echo "  3) ⚙️  创建配置备份"
    echo "  4) 🔄 恢复备份"
    echo "  5) 📋 列出备份文件"
    echo "  6) 🗑️  删除备份文件"
    echo "  7) ❌ 退出"
    echo ""
}

# 创建备份目录
create_backup_dir() {
    local backup_dir="backups"
    if [ ! -d "$backup_dir" ]; then
        mkdir -p "$backup_dir"
        log_info "创建备份目录: $backup_dir"
    fi
    echo "$backup_dir"
}

# 创建完整备份
create_full_backup() {
    log_info "创建完整备份..."
    
    local backup_dir=$(create_backup_dir)
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="full_backup_$timestamp"
    local backup_path="$backup_dir/$backup_name"
    
    mkdir -p "$backup_path"
    
    echo ""
    log_info "备份内容："
    echo "  - 配置文件"
    echo "  - 环境变量"
    echo "  - 前端构建文件"
    echo "  - Docker 镜像"
    echo "  - 容器数据"
    echo ""
    
    # 备份配置文件
    log_info "备份配置文件..."
    cp docker-compose.yml "$backup_path/"
    cp .env "$backup_path/"
    
    # 备份脚本文件
    log_info "备份脚本文件..."
    cp *.sh "$backup_path/" 2>/dev/null || true
    cp *.md "$backup_path/" 2>/dev/null || true
    
    # 备份前端构建文件
    if [ -d "frontend/dist" ]; then
        log_info "备份前端构建文件..."
        cp -r frontend/dist "$backup_path/"
    fi
    
    # 备份 Docker 镜像
    log_info "备份 Docker 镜像..."
    docker save inspirflow-fastapi-backend -o "$backup_path/backend_image.tar" 2>/dev/null || log_warning "后端镜像备份失败"
    docker save inspirflow-fastapi-frontend -o "$backup_path/frontend_image.tar" 2>/dev/null || log_warning "前端镜像备份失败"
    
    # 备份容器数据卷
    log_info "备份容器数据..."
    if $COMPOSE_CMD ps -q | grep -q .; then
        $COMPOSE_CMD exec backend tar czf /tmp/backend_data.tar.gz /app/logs /app/static 2>/dev/null || true
        docker cp $($COMPOSE_CMD ps -q backend):/tmp/backend_data.tar.gz "$backup_path/" 2>/dev/null || log_warning "后端数据备份失败"
    fi
    
    # 创建备份信息文件
    cat > "$backup_path/backup_info.txt" << EOF
备份信息
========
备份时间: $(date)
备份类型: 完整备份
备份版本: $(git rev-parse HEAD 2>/dev/null || echo "未知")
Docker Compose 版本: $($COMPOSE_CMD version --short)
系统信息: $(uname -a)

备份内容:
- 配置文件 (docker-compose.yml, .env)
- 脚本文件 (*.sh, *.md)
- 前端构建文件 (frontend/dist)
- Docker 镜像 (backend_image.tar, frontend_image.tar)
- 容器数据 (backend_data.tar.gz)
EOF
    
    # 压缩备份
    log_info "压缩备份文件..."
    cd "$backup_dir"
    tar czf "$backup_name.tar.gz" "$backup_name"
    rm -rf "$backup_name"
    cd ..
    
    log_success "完整备份创建完成: $backup_dir/$backup_name.tar.gz"
    
    # 显示备份大小
    local backup_size=$(du -h "$backup_dir/$backup_name.tar.gz" | cut -f1)
    log_info "备份文件大小: $backup_size"
}

# 创建数据备份
create_data_backup() {
    log_info "创建数据备份..."
    
    local backup_dir=$(create_backup_dir)
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="data_backup_$timestamp"
    local backup_path="$backup_dir/$backup_name"
    
    mkdir -p "$backup_path"
    
    echo ""
    log_info "备份数据内容..."
    
    # 检查服务是否运行
    if ! $COMPOSE_CMD ps -q | grep -q .; then
        log_error "服务未运行，无法备份数据"
        return 1
    fi
    
    # 备份后端数据
    log_info "备份后端数据..."
    $COMPOSE_CMD exec backend tar czf /tmp/backend_data.tar.gz /app/logs /app/static 2>/dev/null || log_warning "后端数据备份失败"
    docker cp $($COMPOSE_CMD ps -q backend):/tmp/backend_data.tar.gz "$backup_path/" 2>/dev/null || log_warning "复制后端数据失败"
    
    # 创建备份信息
    cat > "$backup_path/backup_info.txt" << EOF
数据备份信息
============
备份时间: $(date)
备份类型: 数据备份
备份内容: 应用数据、日志文件、静态文件
EOF
    
    # 压缩备份
    cd "$backup_dir"
    tar czf "$backup_name.tar.gz" "$backup_name"
    rm -rf "$backup_name"
    cd ..
    
    log_success "数据备份创建完成: $backup_dir/$backup_name.tar.gz"
}

# 创建配置备份
create_config_backup() {
    log_info "创建配置备份..."
    
    local backup_dir=$(create_backup_dir)
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="config_backup_$timestamp"
    local backup_path="$backup_dir/$backup_name"
    
    mkdir -p "$backup_path"
    
    echo ""
    log_info "备份配置文件..."
    
    # 备份主要配置文件
    cp docker-compose.yml "$backup_path/"
    cp .env "$backup_path/"
    
    # 备份脚本文件
    cp *.sh "$backup_path/" 2>/dev/null || true
    cp *.md "$backup_path/" 2>/dev/null || true
    
    # 备份前端配置
    if [ -f "frontend/package.json" ]; then
        mkdir -p "$backup_path/frontend"
        cp frontend/package.json "$backup_path/frontend/"
        cp frontend/vite.config.js "$backup_path/frontend/" 2>/dev/null || true
    fi
    
    # 备份后端配置
    if [ -d "backend/app/core" ]; then
        mkdir -p "$backup_path/backend/app/core"
        cp backend/app/core/config.py "$backup_path/backend/app/core/" 2>/dev/null || true
    fi
    
    # 创建备份信息
    cat > "$backup_path/backup_info.txt" << EOF
配置备份信息
============
备份时间: $(date)
备份类型: 配置备份
备份内容: 配置文件、脚本文件、环境变量
EOF
    
    # 压缩备份
    cd "$backup_dir"
    tar czf "$backup_name.tar.gz" "$backup_name"
    rm -rf "$backup_name"
    cd ..
    
    log_success "配置备份创建完成: $backup_dir/$backup_name.tar.gz"
}

# 列出备份文件
list_backups() {
    local backup_dir="backups"
    
    if [ ! -d "$backup_dir" ] || [ -z "$(ls -A $backup_dir 2>/dev/null)" ]; then
        log_info "没有找到备份文件"
        return 0
    fi
    
    echo ""
    echo -e "${GREEN}可用的备份文件：${NC}"
    echo ""
    
    local count=1
    for backup in "$backup_dir"/*.tar.gz; do
        if [ -f "$backup" ]; then
            local filename=$(basename "$backup")
            local size=$(du -h "$backup" | cut -f1)
            local date=$(stat -c %y "$backup" 2>/dev/null | cut -d' ' -f1 || echo "未知")
            
            echo "  $count) $filename"
            echo "     大小: $size"
            echo "     日期: $date"
            echo ""
            
            ((count++))
        fi
    done
}

# 恢复备份
restore_backup() {
    log_info "恢复备份..."
    
    # 列出可用备份
    list_backups
    
    local backup_dir="backups"
    if [ ! -d "$backup_dir" ] || [ -z "$(ls -A $backup_dir 2>/dev/null)" ]; then
        return 0
    fi
    
    echo ""
    read -p "请输入要恢复的备份文件名: " backup_file
    
    if [ -z "$backup_file" ]; then
        log_error "备份文件名不能为空"
        return 1
    fi
    
    local backup_path="$backup_dir/$backup_file"
    if [ ! -f "$backup_path" ]; then
        log_error "备份文件不存在: $backup_path"
        return 1
    fi
    
    echo ""
    log_warning "恢复备份将覆盖当前配置和数据"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复操作已取消"
        return 0
    fi
    
    # 停止服务
    log_info "停止当前服务..."
    $COMPOSE_CMD down 2>/dev/null || true
    
    # 解压备份
    log_info "解压备份文件..."
    local temp_dir="/tmp/restore_$$"
    mkdir -p "$temp_dir"
    tar xzf "$backup_path" -C "$temp_dir"
    
    # 恢复文件
    local restore_dir=$(ls "$temp_dir")
    log_info "恢复配置文件..."
    
    # 备份当前配置
    cp docker-compose.yml docker-compose.yml.bak 2>/dev/null || true
    cp .env .env.bak 2>/dev/null || true
    
    # 恢复配置
    cp "$temp_dir/$restore_dir"/* . 2>/dev/null || true
    
    # 恢复前端构建文件
    if [ -d "$temp_dir/$restore_dir/dist" ]; then
        log_info "恢复前端构建文件..."
        rm -rf frontend/dist
        cp -r "$temp_dir/$restore_dir/dist" frontend/
    fi
    
    # 恢复 Docker 镜像
    if [ -f "$temp_dir/$restore_dir/backend_image.tar" ]; then
        log_info "恢复后端镜像..."
        docker load -i "$temp_dir/$restore_dir/backend_image.tar"
    fi
    
    if [ -f "$temp_dir/$restore_dir/frontend_image.tar" ]; then
        log_info "恢复前端镜像..."
        docker load -i "$temp_dir/$restore_dir/frontend_image.tar"
    fi
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    log_success "备份恢复完成"
    echo ""
    log_info "建议执行以下操作："
    echo "  1. 检查配置文件"
    echo "  2. 启动服务: docker compose up -d"
    echo "  3. 验证服务状态: ./deployment-summary.sh"
}

# 删除备份文件
delete_backup() {
    log_info "删除备份文件..."
    
    # 列出可用备份
    list_backups
    
    local backup_dir="backups"
    if [ ! -d "$backup_dir" ] || [ -z "$(ls -A $backup_dir 2>/dev/null)" ]; then
        return 0
    fi
    
    echo ""
    read -p "请输入要删除的备份文件名 (或输入 'all' 删除所有): " backup_file
    
    if [ -z "$backup_file" ]; then
        log_error "备份文件名不能为空"
        return 1
    fi
    
    if [ "$backup_file" = "all" ]; then
        echo ""
        log_warning "这将删除所有备份文件"
        read -p "确认继续？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$backup_dir"
            log_success "所有备份文件已删除"
        else
            log_info "操作已取消"
        fi
    else
        local backup_path="$backup_dir/$backup_file"
        if [ ! -f "$backup_path" ]; then
            log_error "备份文件不存在: $backup_path"
            return 1
        fi
        
        echo ""
        log_warning "确认删除备份文件: $backup_file"
        read -p "继续？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -f "$backup_path"
            log_success "备份文件已删除: $backup_file"
        else
            log_info "操作已取消"
        fi
    fi
}

# 主循环
main_loop() {
    while true; do
        show_menu
        read -p "请选择 (1-7): " choice
        
        case $choice in
            1)
                create_full_backup
                ;;
            2)
                create_data_backup
                ;;
            3)
                create_config_backup
                ;;
            4)
                restore_backup
                ;;
            5)
                list_backups
                ;;
            6)
                delete_backup
                ;;
            7)
                log_info "退出备份恢复工具"
                exit 0
                ;;
            *)
                log_error "无效选择，请输入 1-7"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 键继续..."
        clear
        show_header
    done
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_docker_compose
    
    # 清屏并显示标题
    clear
    show_header
    
    # 进入主循环
    main_loop
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
