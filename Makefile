# InspirFlow FastAPI Makefile
# 简化常用开发和部署操作

.PHONY: help install dev build test clean deploy

# 默认目标
help:
	@echo "InspirFlow FastAPI 项目管理"
	@echo "=========================="
	@echo ""
	@echo "可用命令:"
	@echo "  help        显示此帮助信息"
	@echo "  install     安装所有依赖"
	@echo "  dev         启动开发环境"
	@echo "  backend     仅启动后端服务"
	@echo "  frontend    仅启动前端服务"
	@echo "  build       构建生产版本"
	@echo "  test        运行测试"
	@echo "  clean       清理临时文件"
	@echo "  setup       初始化项目环境"
	@echo "  check       检查系统状态"
	@echo ""

# 安装依赖
install:
	@echo "📦 安装项目依赖..."
	@echo "安装后端依赖..."
	cd backend && pip install -r requirements.txt
	@echo "安装前端依赖..."
	cd frontend && npm install
	@echo "✅ 依赖安装完成"

# 启动开发环境
dev:
	@echo "🚀 启动开发环境..."
	python start_all.py

# 仅启动后端
backend:
	@echo "🔧 启动后端服务..."
	python start_backend.py

# 仅启动前端
frontend:
	@echo "🎨 启动前端服务..."
	python start_frontend.py

# 构建生产版本
build:
	@echo "🏗️  构建生产版本..."
	@echo "构建前端..."
	cd frontend && npm run build
	@echo "✅ 构建完成"

# 运行测试
test:
	@echo "🧪 运行系统测试..."
	python test_system.py

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name ".pytest_cache" -delete
	find . -type f -name ".DS_Store" -delete
	cd frontend && rm -rf node_modules/.cache
	cd frontend && rm -rf dist
	@echo "✅ 清理完成"

# 初始化项目环境
setup:
	@echo "⚙️  初始化项目环境..."
	@echo "检查 Python 版本..."
	@python3 --version || (echo "❌ 需要 Python 3.8+"; exit 1)
	@echo "检查 Node.js 版本..."
	@node --version || (echo "❌ 需要 Node.js 16+"; exit 1)
	@echo "创建环境配置文件..."
	@if [ ! -f backend/.env ]; then \
		cp backend/.env.example backend/.env; \
		echo "✅ 已创建 backend/.env"; \
	else \
		echo "⚠️  backend/.env 已存在"; \
	fi
	@if [ ! -f frontend/.env ]; then \
		cp frontend/.env.example frontend/.env; \
		echo "✅ 已创建 frontend/.env"; \
	else \
		echo "⚠️  frontend/.env 已存在"; \
	fi
	@echo "安装依赖..."
	@make install
	@echo "✅ 项目环境初始化完成"

# 检查系统状态
check:
	@echo "🔍 检查系统状态..."
	@echo "检查后端服务..."
	@curl -s http://localhost:20010/health > /dev/null && echo "✅ 后端服务正常" || echo "❌ 后端服务未运行"
	@echo "检查前端服务..."
	@curl -s http://localhost:20020 > /dev/null && echo "✅ 前端服务正常" || echo "❌ 前端服务未运行"

# 格式化代码
format:
	@echo "🎨 格式化代码..."
	@echo "格式化 Python 代码..."
	cd backend && python -m black . --line-length 88
	cd backend && python -m isort .
	@echo "格式化前端代码..."
	cd frontend && npm run format
	@echo "✅ 代码格式化完成"

# 代码检查
lint:
	@echo "🔍 代码质量检查..."
	@echo "检查 Python 代码..."
	cd backend && python -m flake8 .
	cd backend && python -m mypy .
	@echo "检查前端代码..."
	cd frontend && npm run lint
	@echo "✅ 代码检查完成"

# 更新依赖
update:
	@echo "📦 更新依赖..."
	@echo "更新后端依赖..."
	cd backend && pip list --outdated
	@echo "更新前端依赖..."
	cd frontend && npm outdated
	@echo "💡 请手动更新需要的依赖"

# 备份项目
backup:
	@echo "💾 备份项目..."
	@DATE=$$(date +%Y%m%d_%H%M%S); \
	tar -czf "inspirflow_backup_$$DATE.tar.gz" \
		--exclude=node_modules \
		--exclude=__pycache__ \
		--exclude=.git \
		--exclude="*.pyc" \
		--exclude=dist \
		--exclude=.env \
		.; \
	echo "✅ 备份完成: inspirflow_backup_$$DATE.tar.gz"

# 部署到生产环境
deploy:
	@echo "🚀 部署到生产环境..."
	@echo "构建前端..."
	@make build
	@echo "重启服务..."
	sudo systemctl restart inspirflow-backend
	sudo systemctl reload nginx
	@echo "✅ 部署完成"

# 查看日志
logs:
	@echo "📋 查看服务日志..."
	@echo "后端日志:"
	@tail -f backend/logs/app.log 2>/dev/null || echo "日志文件不存在"

# 数据库迁移 (如果使用数据库)
migrate:
	@echo "🗄️  数据库迁移..."
	cd backend && python -m alembic upgrade head
	@echo "✅ 数据库迁移完成"

# 生成 API 文档
docs:
	@echo "📚 生成 API 文档..."
	@echo "后端 API 文档: http://localhost:20010/docs"
	@echo "前端组件文档: (待实现)"

# 性能测试
benchmark:
	@echo "⚡ 性能测试..."
	@echo "测试后端 API..."
	@ab -n 1000 -c 10 http://localhost:20010/health || echo "请安装 apache2-utils"
	@echo "✅ 性能测试完成"
