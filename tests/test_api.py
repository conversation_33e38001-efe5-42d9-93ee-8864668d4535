#!/usr/bin/env python3
"""
测试新增的API端点
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:20010"
API_KEY = "test-123"  # 使用测试前缀以触发后端测试用户模式

def test_update_conversation_title():
    """测试更新对话标题API"""
    print("🧪 测试更新对话标题API...")
    
    # 首先获取对话列表
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    # 获取对话列表
    response = requests.get(f"{BASE_URL}/api/v1/conversations/", headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取对话列表失败: {response.status_code}")
        return
    
    conversations = response.json().get("conversations", [])
    if not conversations:
        print("❌ 没有找到对话")
        return
    
    # 使用第一个对话进行测试
    conversation_id = conversations[0]["id"]
    print(f"📝 使用对话ID: {conversation_id}")
    
    # 测试更新标题
    update_data = {"title": "测试自动命名功能"}
    response = requests.post(
        f"{BASE_URL}/api/v1/conversations/{conversation_id}/title",
        headers=headers,
        json=update_data
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 更新对话标题成功: {result}")
    else:
        print(f"❌ 更新对话标题失败: {response.status_code} - {response.text}")

def test_delete_message():
    """测试删除消息API"""
    print("\n🧪 测试删除消息API...")
    
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    # 这里我们只测试API端点是否存在，不实际删除消息
    # 因为我们需要一个真实的消息ID
    print("ℹ️ 删除消息API端点已添加，需要真实的消息ID进行测试")
    print("   端点: DELETE /api/v1/chat/messages/{message_id}")

if __name__ == "__main__":
    print("🚀 开始测试新增的API端点...\n")
    
    try:
        test_update_conversation_title()
        test_delete_message()
        print("\n✅ API测试完成")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
