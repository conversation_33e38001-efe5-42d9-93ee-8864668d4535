#!/usr/bin/env python3
"""
测试代码围栏渲染和MinIO URL问题的修复
"""

import requests
import json
import time
from typing import Dict, Any

# 配置
BASE_URL = "http://localhost:20010"
TEST_API_KEY = "test-code-fence-fix"  # 使用test-前缀触发测试模式

def test_login() -> str:
    """测试登录并返回token"""
    print("🔐 测试登录...")

    # 使用API密钥登录
    login_data = {
        "api_key": TEST_API_KEY
    }

    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)

    if response.status_code == 200:
        token = response.json().get("access_token")
        user_info = response.json().get("user", {})
        print(f"✅ 登录成功，获得token: {token[:20]}...")
        print(f"   用户ID: {user_info.get('id')}")
        print(f"   API密钥: {user_info.get('api_key')}")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def test_create_conversation(token: str) -> int:
    """创建测试对话"""
    print("💬 创建测试对话...")

    headers = {"Authorization": f"Bearer {token}"}
    data = {"title": "代码围栏测试对话"}

    response = requests.post(f"{BASE_URL}/api/v1/conversations/", json=data, headers=headers)

    print(f"   响应状态码: {response.status_code}")
    print(f"   响应内容: {response.text}")

    if response.status_code == 200:
        response_data = response.json()
        conversation_id = response_data.get("conversation", {}).get("id")
        print(f"✅ 对话创建成功，ID: {conversation_id}")
        return conversation_id
    else:
        print(f"❌ 创建对话失败: {response.status_code} - {response.text}")
        return None

def test_send_code_message(token: str, conversation_id: int) -> Dict[str, Any]:
    """发送包含代码块的消息"""
    print("📝 发送包含代码块的消息...")
    
    # 包含多种语言的代码块
    test_message = """
请帮我分析这些代码：

```python
def hello_world():
    print("Hello, World!")
    return "success"
```

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
    return true;
}
```

```c++
#include <iostream>
using namespace std;

int main() {
    cout << "Hello World!" << endl;
    return 0;
}
```

```c#
using System;

class Program {
    static void Main() {
        Console.WriteLine("Hello World!");
    }
}
```

```objective-c
#import <Foundation/Foundation.h>

int main() {
    NSLog(@"Hello World!");
    return 0;
}
```

这些代码有什么共同点？
"""
    
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "conversation_id": conversation_id,
        "message": test_message,
        "model_name": "gpt-4.1"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/chat/", json=data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 消息发送成功")
        print(f"   用户消息ID: {result.get('user_message_id')}")
        print(f"   AI消息ID: {result.get('assistant_message_id')}")
        print(f"   完整响应: {result}")

        # 检查用户消息的URL
        user_msg = result.get('user_message', {})
        if user_msg:
            user_content_url = user_msg.get('content_url', 'N/A')
            user_html_url = user_msg.get('plain_html_url', 'N/A')
            print(f"   用户消息URL: {user_content_url}")
            print(f"   用户消息HTML URL: {user_html_url}")

            # 检查URL长度
            if user_content_url != 'N/A':
                print(f"   用户消息URL长度: {len(user_content_url)} 字符")
            if user_html_url != 'N/A':
                print(f"   用户HTML URL长度: {len(user_html_url)} 字符")

        # 检查AI消息的URL
        ai_msg = result.get('assistant_message', {})
        if ai_msg:
            ai_content_url = ai_msg.get('content_url', 'N/A')
            ai_html_url = ai_msg.get('plain_html_url', 'N/A')
            print(f"   AI消息URL: {ai_content_url}")
            print(f"   AI消息HTML URL: {ai_html_url}")

            # 检查URL长度
            if ai_content_url != 'N/A':
                print(f"   AI消息URL长度: {len(ai_content_url)} 字符")
            if ai_html_url != 'N/A':
                print(f"   AI HTML URL长度: {len(ai_html_url)} 字符")

        # 立即测试URL访问
        print("\n🔗 立即测试URL访问:")
        if user_msg and user_msg.get('plain_html_url'):
            test_content_proxy(user_msg['plain_html_url'])
        if ai_msg and ai_msg.get('plain_html_url'):
            test_content_proxy(ai_msg['plain_html_url'])

        return result
    else:
        print(f"❌ 发送消息失败: {response.status_code} - {response.text}")
        return None

def test_get_messages(token: str, conversation_id: int):
    """获取对话消息列表"""
    print("📋 获取对话消息列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/v1/chat/messages/{conversation_id}", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        messages = result.get('messages', [])
        print(f"✅ 获取到 {len(messages)} 条消息")
        
        for i, msg in enumerate(messages):
            print(f"   消息 {i+1}:")
            print(f"     ID: {msg.get('id')}")
            print(f"     角色: {msg.get('role')}")
            print(f"     是否错误: {msg.get('is_error', False)}")
            print(f"     错误信息: {msg.get('error_info', 'N/A')}")
            print(f"     内容URL: {msg.get('content_url', 'N/A')}")
            print(f"     HTML URL: {msg.get('plain_html_url', 'N/A')}")
            
            # 检查URL长度
            content_url = msg.get('content_url', '')
            if content_url:
                print(f"     URL长度: {len(content_url)} 字符")
                if len(content_url) > 200:
                    print(f"     ⚠️  URL较长，可能需要优化")
                else:
                    print(f"     ✅ URL长度合理")
        
        return messages
    else:
        print(f"❌ 获取消息失败: {response.status_code} - {response.text}")
        return None

def test_content_proxy(url: str):
    """测试内容代理访问"""
    if not url or url.startswith('mock://'):
        print("⚠️  跳过模拟URL测试")
        return
    
    print(f"🔗 测试内容代理访问: {url[:50]}...")
    
    proxy_url = f"{BASE_URL}/api/v1/content/proxy?url={requests.utils.quote(url)}"
    
    try:
        response = requests.get(proxy_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 代理访问成功，内容长度: {len(content)} 字符")
            
            # 检查是否包含代码块结构
            if '<pre><code' in content:
                print("✅ 发现代码块结构 <pre><code>")
                
                # 统计代码块数量
                import re
                code_blocks = re.findall(r'<pre><code[^>]*>', content)
                print(f"   代码块数量: {len(code_blocks)}")
                
                # 检查语言标识
                for block in code_blocks:
                    lang_match = re.search(r'class="language-([^"]+)"', block)
                    if lang_match:
                        print(f"   发现语言: {lang_match.group(1)}")
            else:
                print("⚠️  未发现代码块结构")
                
        else:
            print(f"❌ 代理访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 代理访问异常: {e}")

def main():
    """主测试流程"""
    print("🚀 开始测试代码围栏渲染和MinIO URL修复...")
    print("=" * 60)
    
    # 1. 登录
    token = test_login()
    if not token:
        print("❌ 无法获取token，测试终止")
        return
    
    print()
    
    # 2. 创建对话
    conversation_id = test_create_conversation(token)
    if not conversation_id:
        print("❌ 无法创建对话，测试终止")
        return
    
    print()
    
    # 3. 发送包含代码块的消息
    chat_result = test_send_code_message(token, conversation_id)
    if not chat_result:
        print("❌ 无法发送消息，测试终止")
        return
    
    print()
    
    # 等待处理完成
    print("⏳ 等待消息处理完成...")
    time.sleep(3)
    
    # 4. 获取消息列表
    messages = test_get_messages(token, conversation_id)
    if not messages:
        print("❌ 无法获取消息列表")
        return
    
    print()
    
    # 5. 测试内容代理访问
    for msg in messages:
        if msg.get('plain_html_url'):
            test_content_proxy(msg['plain_html_url'])
            print()
    
    print("=" * 60)
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
