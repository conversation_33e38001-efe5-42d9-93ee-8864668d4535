<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InspirFlow 前端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🚀 InspirFlow 前端测试页面</h1>
    
    <div class="test-section">
        <h2>📊 服务状态检查</h2>
        <button onclick="checkServices()">检查服务状态</button>
        <div id="serviceStatus"></div>
    </div>

    <div class="test-section">
        <h2>🌐 前端页面预览</h2>
        <button onclick="loadFrontend()">加载前端页面</button>
        <button onclick="reloadFrontend()">重新加载</button>
        <div id="frontendContainer">
            <p class="info">点击"加载前端页面"按钮来预览应用</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 API 测试</h2>
        <button onclick="testHealthAPI()">测试健康检查</button>
        <button onclick="testAuthAPI()">测试认证API</button>
        <div id="apiResults"></div>
    </div>

    <div class="test-section">
        <h2>📝 测试日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="testLog" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        async function checkServices() {
            const statusDiv = document.getElementById('serviceStatus');
            statusDiv.innerHTML = '<p class="info">正在检查服务状态...</p>';
            log('开始检查服务状态');

            const services = [
                { name: '前端服务', url: 'http://localhost:20020', port: 20020 },
                { name: '后端服务', url: 'http://localhost:20010/health', port: 20010 }
            ];

            let results = '';
            for (const service of services) {
                try {
                    const response = await fetch(service.url, { 
                        method: 'GET',
                        mode: 'no-cors' // 避免CORS问题
                    });
                    results += `<div class="success">✅ ${service.name} (端口 ${service.port}): 运行正常</div>`;
                    log(`${service.name} 运行正常`);
                } catch (error) {
                    results += `<div class="error">❌ ${service.name} (端口 ${service.port}): 连接失败 - ${error.message}</div>`;
                    log(`${service.name} 连接失败: ${error.message}`, 'error');
                }
            }
            statusDiv.innerHTML = results;
        }

        function loadFrontend() {
            const container = document.getElementById('frontendContainer');
            container.innerHTML = '<iframe src="http://localhost:20020" title="InspirFlow 前端"></iframe>';
            log('加载前端页面');
        }

        function reloadFrontend() {
            const iframe = document.querySelector('#frontendContainer iframe');
            if (iframe) {
                iframe.src = iframe.src;
                log('重新加载前端页面');
            } else {
                loadFrontend();
            }
        }

        async function testHealthAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p class="info">正在测试健康检查API...</p>';
            log('测试健康检查API');

            try {
                const response = await fetch('http://localhost:20010/health');
                const data = await response.json();
                resultsDiv.innerHTML = `
                    <div class="success">✅ 健康检查API响应正常</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                log('健康检查API测试成功');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ 健康检查API测试失败: ${error.message}</div>`;
                log(`健康检查API测试失败: ${error.message}`, 'error');
            }
        }

        async function testAuthAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p class="info">正在测试认证API...</p>';
            log('测试认证API');

            try {
                const response = await fetch('http://localhost:20010/api/v1/auth/me');
                const data = await response.json();
                if (response.status === 403) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ 认证API响应正常 (未认证状态)</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    log('认证API测试成功 (返回未认证状态)');
                } else {
                    resultsDiv.innerHTML = `
                        <div class="info">ℹ️ 认证API响应: ${response.status}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    log(`认证API返回状态: ${response.status}`);
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ 认证API测试失败: ${error.message}</div>`;
                log(`认证API测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            log('测试页面加载完成');
            checkServices();
        };
    </script>
</body>
</html>
