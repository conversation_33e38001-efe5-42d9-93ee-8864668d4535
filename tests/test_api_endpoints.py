#!/usr/bin/env python3
"""
API端点测试脚本
测试所有主要的API端点是否正常工作
"""

import requests
import json
import sys
from datetime import datetime

# 配置
BASE_URL = "http://localhost:20010"
API_KEY = "admin-api-key-change-in-production"

def call_api_endpoint(method, endpoint, data=None, headers=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    if headers is None:
        headers = {}
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers, timeout=10)
        else:
            return False, f"不支持的HTTP方法: {method}"
        
        return True, {
            "status_code": response.status_code,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
        }
    except Exception as e:
        return False, str(e)

def main():
    """主测试函数"""
    print("🧪 InspirFlow API端点测试")
    print("=" * 50)
    print(f"📍 测试地址: {BASE_URL}")
    print(f"🔑 API密钥: {API_KEY}")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 首先登录获取token
    print("\n1. 🔐 测试登录...")
    success, result = call_api_endpoint("POST", "/api/v1/auth/login/", {
        "api_key": API_KEY
    })
    
    if not success:
        print(f"❌ 登录失败: {result}")
        return False
    
    if result["status_code"] != 200:
        print(f"❌ 登录失败: HTTP {result['status_code']}")
        print(f"   响应: {result['response']}")
        return False
    
    token = result["response"]["access_token"]
    print(f"✅ 登录成功，获取到token")
    
    # 设置认证头
    auth_headers = {"Authorization": f"Bearer {token}"}
    
    # 测试端点列表
    test_cases = [
        ("GET", "/health", None, None, "健康检查"),
        ("GET", "/api/v1/models/", None, auth_headers, "获取模型列表"),
        ("GET", "/api/v1/conversations/", None, auth_headers, "获取对话列表"),
        ("POST", "/api/v1/conversations/", {"title": "测试对话"}, auth_headers, "创建新对话"),
    ]
    
    results = []
    
    for i, (method, endpoint, data, headers, description) in enumerate(test_cases, 2):
        print(f"\n{i}. 🔍 测试{description}...")
        success, result = call_api_endpoint(method, endpoint, data, headers)
        
        if not success:
            print(f"❌ {description}失败: {result}")
            results.append(False)
            continue
        
        if result["status_code"] in [200, 201]:
            print(f"✅ {description}成功")
            results.append(True)
        else:
            print(f"❌ {description}失败: HTTP {result['status_code']}")
            print(f"   响应: {result['response']}")
            results.append(False)
    
    # 测试聊天消息（如果有对话）
    print(f"\n{len(test_cases) + 2}. 💬 测试获取聊天消息...")
    success, result = test_api_endpoint("GET", "/api/v1/conversations/", None, auth_headers)
    
    if success and result["status_code"] == 200:
        conversations = result["response"].get("conversations", [])
        if conversations:
            conversation_id = conversations[0]["id"]
            success, result = test_api_endpoint("GET", f"/api/v1/chat/messages/{conversation_id}", None, auth_headers)
            
            if success and result["status_code"] == 200:
                print(f"✅ 获取聊天消息成功")
                results.append(True)
            else:
                print(f"❌ 获取聊天消息失败: HTTP {result.get('status_code', 'N/A')}")
                results.append(False)
        else:
            print("⚠️  没有对话可测试")
            results.append(True)  # 没有对话不算失败
    else:
        print("❌ 无法获取对话列表来测试聊天消息")
        results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    passed = sum(results)
    total = len(results)
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！API端点工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关服务。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
