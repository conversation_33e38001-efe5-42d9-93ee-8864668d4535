<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 登录调试测试</h1>
        <p>测试前端登录API调用，诊断超时问题</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="apiUrl">API基础URL:</label>
                <input type="text" id="apiUrl" value="http://43.155.146.157:20010/api/v1" />
            </div>
            
            <div class="form-group">
                <label for="apiKey">API密钥:</label>
                <input type="password" id="apiKey" value="admin-api-key-change-in-production" />
            </div>
            
            <div class="form-group">
                <label for="timeout">超时时间 (秒):</label>
                <input type="text" id="timeout" value="30" />
            </div>
            
            <button type="submit" id="loginBtn">🔐 测试登录</button>
            <button type="button" id="testConnBtn">🌐 测试连接</button>
            <button type="button" id="clearBtn">🗑️ 清除结果</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const form = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const testConnBtn = document.getElementById('testConnBtn');
        const clearBtn = document.getElementById('clearBtn');
        const resultDiv = document.getElementById('result');
        
        function showResult(message, type = 'info') {
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function appendResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const newContent = `[${timestamp}] ${message}\n\n`;
            resultDiv.textContent += newContent;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        async function testConnection(apiUrl) {
            try {
                appendResult('🔍 测试API连接...', 'info');
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);
                
                const response = await fetch(`${apiUrl.replace('/api/v1', '')}/health`, {
                    method: 'GET',
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (response.ok) {
                    const data = await response.json();
                    appendResult(`✅ API连接成功！\n状态: ${data.status}\n版本: ${data.version}`, 'success');
                    return true;
                } else {
                    appendResult(`❌ API连接失败！\n状态码: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    appendResult('❌ API连接超时！', 'error');
                } else {
                    appendResult(`❌ API连接错误: ${error.message}`, 'error');
                }
                return false;
            }
        }
        
        async function testLogin(apiUrl, apiKey, timeoutSeconds) {
            try {
                appendResult('🔐 开始登录测试...', 'info');
                loginBtn.disabled = true;
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                    appendResult(`⏰ 登录请求超时 (${timeoutSeconds}秒)`, 'warning');
                }, timeoutSeconds * 1000);
                
                const startTime = Date.now();
                
                const response = await fetch(`${apiUrl}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        api_key: apiKey
                    }),
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                appendResult(`📊 请求耗时: ${duration}ms`, 'info');
                
                const data = await response.json();
                
                if (response.ok) {
                    appendResult(`✅ 登录成功！
状态码: ${response.status}
消息: ${data.message}
用户ID: ${data.user?.id}
Token类型: ${data.token_type}
Token长度: ${data.access_token?.length} 字符`, 'success');
                } else {
                    appendResult(`❌ 登录失败！
状态码: ${response.status}
错误信息: ${data.detail || data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    appendResult(`⏰ 登录请求被中止 (超时)`, 'warning');
                } else {
                    appendResult(`❌ 登录网络错误: ${error.message}`, 'error');
                }
            } finally {
                loginBtn.disabled = false;
            }
        }
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const timeout = parseInt(document.getElementById('timeout').value) || 30;
            
            if (!apiUrl || !apiKey) {
                showResult('请填写API URL和API密钥', 'error');
                return;
            }
            
            showResult('', 'info');
            appendResult('🚀 开始登录调试测试', 'info');
            appendResult(`配置信息:
API URL: ${apiUrl}
API密钥: ${apiKey}
超时时间: ${timeout}秒`, 'info');
            
            // 先测试连接
            const connected = await testConnection(apiUrl);
            if (connected) {
                // 连接成功，测试登录
                await testLogin(apiUrl, apiKey, timeout);
            }
        });
        
        testConnBtn.addEventListener('click', async () => {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            if (!apiUrl) {
                showResult('请填写API URL', 'error');
                return;
            }
            
            showResult('', 'info');
            appendResult('🌐 测试API连接', 'info');
            await testConnection(apiUrl);
        });
        
        clearBtn.addEventListener('click', () => {
            showResult('', 'info');
        });
        
        // 页面加载时显示当前配置
        window.addEventListener('load', () => {
            showResult(`📋 登录调试工具已就绪
当前配置:
API URL: ${document.getElementById('apiUrl').value}
API密钥: ${document.getElementById('apiKey').value}
超时时间: ${document.getElementById('timeout').value}秒

点击"测试连接"检查API可达性
点击"测试登录"进行完整登录测试`, 'info');
        });
    </script>
</body>
</html>
