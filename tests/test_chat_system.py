#!/usr/bin/env python3
"""
测试聊天系统的完整功能
包括对话管理、消息处理、URL生成等
"""

import asyncio
import httpx
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:20010"
API_KEY = "admin-api-key-change-in-production"

async def test_chat_system():
    """测试聊天系统的完整功能"""
    print("🧪 开始测试聊天系统...")
    
    async with httpx.AsyncClient() as client:
        # 1. 测试用户登录
        print("\n1️⃣ 测试用户登录")
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/auth/login",
                json={"api_key": API_KEY},
                timeout=10.0
            )
            if response.status_code == 200:
                login_data = response.json()
                user_info = login_data.get("user", {})
                access_token = login_data.get("access_token")
                print(f"✅ 登录成功")
                print(f"   用户ID: {user_info.get('id')}")
                print(f"   当前对话ID: {user_info.get('current_conversation_id')}")
                print(f"   MathJax设置: {user_info.get('mathjax')}")

                # 设置认证头
                headers = {"Authorization": f"Bearer {access_token}"}
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return
        
        # 2. 测试获取对话列表
        print("\n2️⃣ 测试获取对话列表")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/conversations/",
                headers=headers,
                timeout=10.0
            )
            if response.status_code == 200:
                conversations_data = response.json()
                conversations = conversations_data.get("conversations", [])
                print(f"✅ 获取对话列表成功: {len(conversations)} 个对话")
                if conversations:
                    latest_conv = conversations[0]
                    print(f"   最新对话: {latest_conv.get('title')} (ID: {latest_conv.get('id')})")
                    conversation_id = latest_conv.get('id')
                else:
                    print("   暂无对话，将创建新对话")
                    conversation_id = None
            else:
                print(f"❌ 获取对话列表失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 获取对话列表异常: {e}")
            return
        
        # 3. 创建新对话（如果需要）
        if not conversation_id:
            print("\n3️⃣ 测试创建新对话")
            try:
                response = await client.post(
                    f"{BASE_URL}/api/v1/conversations/",
                    headers=headers,
                    json={"title": f"测试对话 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"},
                    timeout=10.0
                )
                if response.status_code == 200:
                    conv_data = response.json()
                    conversation_id = conv_data.get("id")
                    print(f"✅ 创建对话成功: ID={conversation_id}")
                else:
                    print(f"❌ 创建对话失败: {response.status_code}")
                    return
            except Exception as e:
                print(f"❌ 创建对话异常: {e}")
                return
        
        # 4. 测试设置当前对话
        print(f"\n4️⃣ 测试设置当前对话 (ID: {conversation_id})")
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/conversations/{conversation_id}/set-current",
                headers=headers,
                timeout=10.0
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 设置当前对话成功: {result.get('message')}")
            else:
                print(f"❌ 设置当前对话失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 设置当前对话异常: {e}")
        
        # 5. 测试获取对话消息
        print(f"\n5️⃣ 测试获取对话消息")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/chat/messages/{conversation_id}",
                headers=headers,
                timeout=10.0
            )
            if response.status_code == 200:
                messages_data = response.json()
                messages = messages_data.get("messages", [])
                print(f"✅ 获取消息成功: {len(messages)} 条消息")
                for msg in messages[-3:]:  # 显示最近3条消息
                    print(f"   {msg.get('role')}: {msg.get('content_type')} - {msg.get('content_url', 'N/A')[:50]}...")
            else:
                print(f"❌ 获取消息失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 获取消息异常: {e}")
        
        # 6. 测试发送消息
        print(f"\n6️⃣ 测试发送消息")
        try:
            test_message = "这是一个测试消息，包含数学公式：$E = mc^2$"
            response = await client.post(
                f"{BASE_URL}/api/v1/chat/",
                headers=headers,
                json={
                    "conversation_id": conversation_id,
                    "message": test_message,
                    "model_name": "Gemini Flash 2.5"
                },
                timeout=30.0
            )
            if response.status_code == 200:
                chat_data = response.json()
                print(f"✅ 发送消息成功")
                print(f"   用户消息ID: {chat_data.get('user_message_id')}")
                print(f"   AI消息ID: {chat_data.get('assistant_message_id')}")
                if chat_data.get('response'):
                    print(f"   AI回复: {chat_data.get('response')[:100]}...")
            else:
                print(f"❌ 发送消息失败: {response.status_code}")
                error_data = response.json()
                print(f"   错误详情: {error_data}")
        except Exception as e:
            print(f"❌ 发送消息异常: {e}")
        
        # 7. 再次获取消息，验证新消息
        print(f"\n7️⃣ 验证新消息")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/chat/messages/{conversation_id}",
                headers=headers,
                timeout=10.0
            )
            if response.status_code == 200:
                messages_data = response.json()
                messages = messages_data.get("messages", [])
                print(f"✅ 验证成功: 现在有 {len(messages)} 条消息")
                if messages:
                    latest_msg = messages[-1]
                    print(f"   最新消息类型: {latest_msg.get('content_type')}")
                    print(f"   最新消息URL: {latest_msg.get('content_url', 'N/A')[:80]}...")
            else:
                print(f"❌ 验证失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 验证异常: {e}")
    
    print("\n🎉 聊天系统测试完成！")

if __name__ == "__main__":
    asyncio.run(test_chat_system())
