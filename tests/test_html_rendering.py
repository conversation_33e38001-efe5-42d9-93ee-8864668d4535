#!/usr/bin/env python3
"""
测试HTML渲染改进效果的脚本
"""

import requests
import json
import time

# 配置
BACKEND_URL = "http://localhost:20010"
FRONTEND_URL = "http://localhost:20020"
API_KEY = "admin-api-key-change-in-production"

def get_access_token():
    """获取访问令牌"""
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/v1/auth/login",
            json={"api_key": API_KEY},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get('access_token')
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_problematic_code_message(token):
    """测试包含问题代码的消息渲染"""
    print("🧪 测试包含复杂代码的消息渲染...")
    
    # 包含各种可能有问题的代码内容
    test_content = '''
这是一个包含复杂代码的测试消息：

```python
# 包含特殊字符的Python代码
def complex_function(data):
    """
    这个函数包含各种特殊字符: <>&"'
    """
    html_content = "<div class='test'>Hello & World</div>"
    js_code = 'alert("XSS test: <script>alert(1)</script>");'
    
    # 数学公式
    formula = "E = mc²"
    
    # 正则表达式
    pattern = r'[<>&"\']+'
    
    return {
        "html": html_content,
        "js": js_code,
        "formula": formula,
        "pattern": pattern
    }
```

```javascript
// JavaScript代码包含HTML和特殊字符
function renderHTML() {
    const template = `
        <div class="container">
            <h1>Title with "quotes" & ampersands</h1>
            <script>console.log('embedded script');</script>
            <p>Math: $E = mc^2$</p>
        </div>
    `;
    
    document.body.innerHTML = template;
}
```

```html
<!-- HTML代码包含未闭合标签 -->
<div class="test">
    <p>段落1
    <p>段落2</p>
    <code>内联代码 with <tags>
    <pre>
        预格式化文本
        包含 & 特殊字符
    </pre>
</div>
```

数学公式测试：
- 行内公式：$f(x) = x^2 + 2x + 1$
- 块级公式：$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

特殊字符测试：< > & " ' 
'''

    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 先创建一个对话
        conv_response = requests.post(
            f"{BACKEND_URL}/api/v1/conversations/",
            json={"title": "HTML渲染测试对话"},
            headers=headers,
            timeout=10
        )

        if conv_response.status_code != 200:
            print(f"❌ 创建对话失败: {conv_response.status_code}")
            print(f"   响应: {conv_response.text}")
            return None

        conv_data = conv_response.json()
        print(f"✅ 创建对话响应: {conv_data}")
        conversation_id = conv_data.get('conversation', {}).get('id')
        print(f"✅ 创建对话成功: ID={conversation_id}")

        # 发送消息
        response = requests.post(
            f"{BACKEND_URL}/api/v1/chat/",
            json={
                "message": test_content,
                "conversation_id": conversation_id,
                "model_name": "test-model"
            },
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 消息发送成功")
            print(f"   对话ID: {data.get('conversation_id')}")
            print(f"   消息ID: {data.get('message_id')}")
            
            # 检查消息URLs
            if 'message_urls' in data:
                urls = data['message_urls']
                print(f"   原始URL: {urls.get('original_url', 'N/A')}")
                print(f"   渲染URL: {urls.get('rendered_html_url', 'N/A')}")
                
                # 测试HTML内容获取
                if urls.get('rendered_html_url'):
                    test_html_content(urls['rendered_html_url'])
            
            return data.get('conversation_id')
        else:
            print(f"❌ 消息发送失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 消息发送异常: {e}")
        return None

def test_html_content(url):
    """测试HTML内容获取"""
    print(f"🔍 测试HTML内容获取: {url}")
    
    try:
        # 通过前端代理获取内容
        proxy_url = f"{FRONTEND_URL}/api/v1/content/proxy?url={requests.utils.quote(url)}"
        response = requests.get(proxy_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ HTML内容获取成功: {len(content)} 字符")
            
            # 检查内容是否包含预期的元素
            checks = [
                ('<pre><code class="language-python">', 'Python代码块'),
                ('<pre><code class="language-javascript">', 'JavaScript代码块'),
                ('<pre><code class="language-html">', 'HTML代码块'),
                ('&lt;', 'HTML转义'),
                ('&amp;', '&符号转义'),
                ('&quot;', '引号转义'),
            ]
            
            for check, desc in checks:
                if check in content:
                    print(f"   ✅ {desc}: 找到")
                else:
                    print(f"   ⚠️ {desc}: 未找到")
            
            # 检查是否有明显的HTML结构问题
            if '<script>' in content.lower():
                print(f"   ⚠️ 发现未转义的script标签")
            if content.count('<') != content.count('>'):
                print(f"   ⚠️ HTML标签不匹配")
            
        else:
            print(f"❌ HTML内容获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ HTML内容获取异常: {e}")

def test_frontend_display(conversation_id, token):
    """测试前端显示"""
    print(f"🌐 测试前端显示...")

    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        # 获取对话消息
        response = requests.get(
            f"{BACKEND_URL}/api/v1/chat/messages/{conversation_id}",
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            messages = data.get('messages', [])
            print(f"✅ 获取到 {len(messages)} 条消息")

            for msg in messages:
                print(f"   消息ID: {msg.get('id')}")
                print(f"   角色: {msg.get('role')}")
                print(f"   内容URL: {msg.get('content_url', 'N/A')}")
                print(f"   HTML URL: {msg.get('plain_html_url', 'N/A')}")

                if msg.get('role') == 'assistant' and msg.get('content_url'):
                    test_html_content(msg['content_url'])
        else:
            print(f"❌ 获取消息失败: {response.status_code}")
            print(f"   响应: {response.text}")

    except Exception as e:
        print(f"❌ 前端显示测试异常: {e}")

def main():
    """主测试函数"""
    print("🚀 HTML渲染改进测试开始")
    print("=" * 50)
    
    # 获取访问令牌
    print("🔑 获取访问令牌...")
    token = get_access_token()
    if not token:
        print("❌ 无法获取访问令牌，测试终止")
        return
    
    print("✅ 访问令牌获取成功")
    
    # 测试复杂代码消息
    conversation_id = test_problematic_code_message(token)
    
    if conversation_id:
        # 等待处理完成
        print("⏳ 等待消息处理完成...")
        time.sleep(3)
        
        # 测试前端显示
        test_frontend_display(conversation_id, token)
    
    print("\n" + "=" * 50)
    print("📊 测试完成")
    print("💡 请访问前端页面查看实际显示效果:")
    print(f"   {FRONTEND_URL}")

if __name__ == "__main__":
    main()
