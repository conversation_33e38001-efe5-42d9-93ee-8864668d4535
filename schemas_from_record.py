# schemas.py
"""
Pydantic模式定义
"""
from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from models import MessageRole
from typing import Literal


# 基础模式
class BaseSchema(BaseModel):
    """基础模式"""
    model_config = ConfigDict(from_attributes=True)


# 用户相关模式
class UserBase(BaseSchema):
    """用户基础模式"""
    api_key: str = Field(..., description="用户API密钥")
    is_active: bool = Field(True, description="是否激活")
    permission: int = Field(1, description="权限级别")
    mathjax: bool = Field(False, description="是否启用MathJax")
    current_model_id: Optional[int] = Field(None, description="当前使用的模型ID")
    current_temperature: Decimal = Field(Decimal("0.70"), description="当前温度设置")


class UserCreate(UserBase):
    """创建用户模式"""
    pass


class UserGenerateCreate(BaseSchema):
    """管理员自动生成用户Key时的创建输入"""
    is_active: bool = Field(True, description="是否激活")
    permission: int = Field(1, description="权限级别")
    mathjax: bool = Field(False, description="是否启用MathJax")
    current_model_id: Optional[int] = Field(None, description="当前使用的模型ID")
    current_temperature: Decimal = Field(Decimal("0.70"), description="当前温度设置")


class UserUpdate(BaseSchema):
    """更新用户模式"""
    is_active: Optional[bool] = None
    permission: Optional[int] = None
    mathjax: Optional[bool] = None
    current_model_id: Optional[int] = None
    current_temperature: Optional[Decimal] = None
    current_conversation_id: Optional[int] = None


class UserRecharge(BaseSchema):
    """用户充值模式"""
    amount: Decimal = Field(..., description="充值金额，必须为正数")
    note: Optional[str] = Field(None, description="充值备注")


class UserPartialUpdate(BaseSchema):
    """用户原子级更新模式"""
    is_active: Optional[bool] = Field(None, description="是否激活")
    permission: Optional[int] = Field(None, description="权限级别")
    mathjax: Optional[bool] = Field(None, description="是否启用MathJax")
    current_model_id: Optional[int] = Field(None, description="当前使用的模型ID")
    current_temperature: Optional[Decimal] = Field(None, description="当前温度设置")
    current_conversation_id: Optional[int] = Field(None, description="当前对话ID")


class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    created_at: datetime
    current_conversation_id: Optional[int] = None
    total_deposited: Decimal
    total_spent: Decimal
    current_balance: Decimal
    total_prompt_tokens: int
    total_completion_tokens: int


# 对话相关模式
class ConversationBase(BaseSchema):
    """对话基础模式"""
    title: Optional[str] = Field(None, description="对话标题")


class ConversationCreate(ConversationBase):
    """创建对话模式"""
    user_id: Optional[int] = Field(None, description="用户ID（可选，默认为当前用户）")


class ConversationUpdate(BaseSchema):
    """更新对话模式"""
    title: Optional[str] = None


class ConversationPartialUpdate(BaseSchema):
    """对话原子级更新模式"""
    title: Optional[str] = Field(None, description="对话标题")


class ConversationResponse(ConversationBase):
    """对话响应模式"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    user_id: int
    message_count: Optional[int] = Field(None, description="消息数量")

    @field_validator('updated_at', mode='before')
    @classmethod
    def validate_updated_at(cls, v):
        """处理None值的updated_at字段"""
        if v is None:
            return None
        return v


# 消息相关模式
class MessageBase(BaseSchema):
    """消息基础模式"""
    role: str = Field(..., description="消息角色")
    content_url: str = Field(..., description="消息内容MinIO存储URL")
    affiliated_url: Optional[str] = Field(None, description="关联内容MinIO存储URL（如英文翻译等）")


class MessageCreate(MessageBase):
    """创建消息模式"""
    conversation_id: int = Field(..., description="对话ID")
    model_id: Optional[int] = Field(None, description="使用的模型ID（仅assistant消息）")
    temperature: Optional[Decimal] = Field(None, description="温度参数（仅assistant消息）")
    max_tokens: Optional[int] = Field(None, description="最大token数（仅assistant消息）")
    is_error: bool = Field(False, description="是否出错（仅assistant消息）")
    error_info: Optional[str] = Field(None, description="错误信息（仅assistant消息）")


class MessageUpdate(BaseSchema):
    """更新消息模式"""
    affiliated_url: Optional[str] = None
    is_error: Optional[bool] = None
    error_info: Optional[str] = None


class MessageUrlUpdate(BaseSchema):
    """消息URL原子级更新模式"""
    content_url: Optional[str] = Field(None, description="消息内容MinIO存储URL")
    affiliated_url: Optional[str] = Field(None, description="关联内容MinIO存储URL（如英文翻译等）")


class MessageResponse(BaseSchema):
    """消息响应模式"""
    id: int
    conversation_id: int
    role: str
    created_at: datetime
    updated_at: datetime
    content_url: str
    affiliated_url: Optional[str] = None
    model_id: Optional[int] = None
    temperature: Optional[Decimal] = None
    max_tokens: Optional[int] = None
    is_error: bool = False
    error_info: Optional[str] = None


# 充值记录模式
class RechargeRecordBase(BaseSchema):
    user_id: int
    amount: Decimal
    note: Optional[str] = None
    created_by: Optional[int] = None


class RechargeRecordResponse(RechargeRecordBase):
    id: int
    created_at: datetime


class RechargeRecordListResponse(BaseModel):
    items: List[RechargeRecordResponse]
    total_amount: Decimal
    total: int = 0
    page: int = 1
    size: int = 10


# 计费系统 Schema
class BillingRecordBase(BaseSchema):
    user_id: int
    message_id: Optional[int] = None
    conversation_id: Optional[int] = None
    billing_type: Literal["message", "api_call", "subscription"] = "message"
    model_name: Optional[str] = None
    prompt_tokens: int = 0
    completion_tokens: int = 0
    prompt_cost: Decimal = Decimal('0.000000')
    completion_cost: Decimal = Decimal('0.000000')
    total_cost: Decimal
    description: Optional[str] = None
    billing_metadata: Optional[dict] = None


class BillingRecordCreate(BillingRecordBase):
    pass


class BillingRecordResponse(BillingRecordBase):
    id: int
    created_at: datetime


class BillingRecordListResponse(BaseModel):
    items: List[BillingRecordResponse]
    total: int
    page: int
    size: int


# 带计费信息的消息响应模式
class MessageWithBillingResponse(MessageResponse):
    """带计费信息的消息响应模式"""
    billing: Optional[BillingRecordResponse] = None


# 统计相关模式
class UserStatsResponse(BaseSchema):
    """用户统计响应模式"""
    user_id: int
    total_conversations: int
    total_messages: int
    total_user_messages: int
    total_assistant_messages: int
    total_cost: Decimal
    total_prompt_tokens: int
    total_completion_tokens: int


class ConversationStatsResponse(BaseSchema):
    """对话统计响应模式"""
    conversation_id: int
    total_messages: int
    user_messages: int
    assistant_messages: int
    total_cost: Decimal


# 分页相关模式
class PaginationParams(BaseSchema):
    """分页参数"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")


class PaginatedResponse(BaseSchema):
    """分页响应模式"""
    items: List[BaseSchema]
    total: int
    page: int
    size: int
    pages: int


# 响应模式
class SuccessResponse(BaseSchema):
    """成功响应模式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[BaseSchema] = None


class ErrorResponse(BaseSchema):
    """错误响应模式"""
    success: bool = False
    message: str
    error_code: Optional[str] = None