#!/bin/bash

# InspirFlow 项目管理入口脚本
# 这是项目的主要管理入口，调用 scripts 目录下的管理器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目结构
check_project_structure() {
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    if [ ! -f "scripts/inspirflow-manager.sh" ]; then
        log_error "管理脚本不存在，请检查项目结构"
        exit 1
    fi
}

# 主函数
main() {
    log_info "启动 InspirFlow 管理器..."
    
    check_project_structure
    
    # 切换到 scripts 目录并运行管理器
    cd scripts
    ./inspirflow-manager.sh "$@"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
