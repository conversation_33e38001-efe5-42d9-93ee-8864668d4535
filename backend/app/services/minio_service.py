# app/services/minio_service.py - MinIO服务

import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from minio import Minio
from minio.error import S3Error
import io

from ..core.config import settings

logger = logging.getLogger(__name__)

class MinIOService:
    """MinIO对象存储服务"""

    def __init__(self):
        self.endpoint = settings.MINIO_ENDPOINT
        self.access_key = settings.MINIO_ACCESS_KEY
        self.secret_key = settings.MINIO_SECRET_KEY
        self.bucket_name = settings.MINIO_BUCKET
        self.secure = settings.MINIO_SECURE

        try:
            # 初始化MinIO客户端（仅配置，不进行任何网络请求）
            self.client = Minio(
                self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure
            )
            # 为避免应用启动时阻塞，bucket检查与创建延后到实际使用阶段
            logger.info(f"MinIO客户端已配置（延迟检查bucket）: {self.endpoint}/{self.bucket_name}")
        except Exception as e:
            logger.error(f"MinIO服务初始化失败: {e}")
            # 初始化客户端本身通常不会抛错，这里仅保底记录
            pass

    def _ensure_bucket_exists(self):
        """确保bucket存在并设置公共读取权限（按需调用）"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"创建bucket: {self.bucket_name}")
            # 设置公共读取权限
            self._set_bucket_public_read_policy()
        except S3Error as e:
            logger.error(f"确保bucket存在失败: {e}")
            raise

    def _set_bucket_public_read_policy(self):
        """设置bucket的公共读取权限"""
        try:
            policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": ["s3:GetObject"],
                        "Resource": [f"arn:aws:s3:::{self.bucket_name}/*"]
                    }
                ]
            }

            self.client.set_bucket_policy(self.bucket_name, json.dumps(policy))
            logger.info(f"已设置bucket {self.bucket_name} 的公共读取权限")

        except S3Error as e:
            logger.warning(f"设置bucket公共读取权限失败: {e}")

    def _generate_object_name(self, message_id: int, content_type: str, file_extension: str = "") -> str:
        """生成对象名称"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"messages/{message_id}/{content_type}_{timestamp}{file_extension}"

    def _build_object_http_url(self, object_name: str, bucket: Optional[str] = None) -> str:
        """构造简短的HTTP访问URL（无签名）"""
        proto = "https" if self.secure else "http"
        bkt = bucket or self.bucket_name
        return f"{proto}://{self.endpoint}/{bkt}/{object_name}"

    async def upload_original_content(
        self,
        message_id: int,
        content: str,
        role: str = "user",
        conversation_id: int = None,
        model_display_name: str = None,
        temperature: float = None,
        cost_info: dict = None
    ) -> Optional[str]:
        """上传原始消息内容到MinIO"""
        try:
            # 验证输入参数
            if not content or not content.strip():
                logger.warning(f"消息内容为空 - 消息ID: {message_id}")
                content = ""  # 确保content不是None

            # 创建JSON格式的内容，包含完整的消息头信息
            # 如果是AI消息且有模型显示名称，使用模型名称作为role
            display_role = role
            if role == "assistant" and model_display_name:
                display_role = model_display_name

            content_data = {
                "message_id": message_id,
                "conversation_id": conversation_id,
                "role": display_role,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "format": "original"
            }

            # 如果是AI消息，添加额外信息
            if role == "assistant":
                if temperature is not None:
                    content_data["temperature"] = temperature
                if cost_info:
                    content_data["cost"] = cost_info

            # 序列化JSON
            content_json = json.dumps(content_data, ensure_ascii=False, indent=2)
            content_bytes = content_json.encode('utf-8')

            # 详细日志
            logger.info(f"准备上传原始内容 - 消息ID: {message_id}")
            logger.info(f"  - 内容长度: {len(content)} 字符")
            logger.info(f"  - JSON长度: {len(content_json)} 字节")
            logger.debug(f"  - 原始内容: {repr(content)}")

            # 生成对象名称
            object_name = self._generate_object_name(message_id, "original", ".json")
            logger.info(f"  - 对象名称: {object_name}")

            # 确保bucket存在（按需）
            try:
                self._ensure_bucket_exists()
            except Exception as e:
                logger.warning(f"跳过bucket检查: {e}")
            # 上传到MinIO
            result = self.client.put_object(
                self.bucket_name,
                object_name,
                io.BytesIO(content_bytes),
                len(content_bytes),
                content_type="application/json"
            )
            logger.info(f"  - MinIO上传结果: {result}")

            # 生成简短HTTP访问URL（配合代理端点可正常访问）
            url = self._build_object_http_url(object_name)

            logger.info(f"✅ 原始内容上传成功: {url}")

            # 验证上传结果
            try:
                # 立即尝试下载验证
                response = self.client.get_object(self.bucket_name, object_name)
                downloaded_content = response.read().decode('utf-8')
                downloaded_data = json.loads(downloaded_content)

                if downloaded_data.get("content") != content:
                    logger.error(f"❌ 上传验证失败 - 内容不匹配")
                    logger.error(f"  - 原始: {repr(content)}")
                    logger.error(f"  - 下载: {repr(downloaded_data.get('content'))}")
                else:
                    logger.info(f"✅ 上传验证成功 - 内容匹配")

            except Exception as verify_error:
                logger.warning(f"⚠️ 上传验证失败: {verify_error}")

            return url

        except Exception as e:
            logger.error(f"❌ 上传原始内容失败: {e}")
            logger.error(f"  - 消息ID: {message_id}")
            logger.error(f"  - 内容: {repr(content)}")
            import traceback
            logger.error(f"  - 堆栈: {traceback.format_exc()}")
            return None













    async def process_message_content(
        self,
        message_id: int,
        content: str,
        role: str = "assistant",
        conversation_id: int = None,
        model_display_name: str = None,
        temperature: float = None,
        cost_info: dict = None
    ) -> Dict[str, Optional[str]]:
        """处理消息内容，只生成原始内容URL（前端渲染模式）

        Args:
            message_id: 消息ID
            content: 原始消息内容（markdown格式）
            role: 消息角色（user/assistant）
            conversation_id: 对话ID
            model_display_name: 模型显示名称（仅AI消息）
            temperature: 温度参数（仅AI消息）
            cost_info: 费用信息（仅AI消息）

        Returns:
            包含URL的字典：
            - original_url: 原始JSON格式（用于前端Markdown渲染）
        """
        logger.info(f"🔄 开始处理消息内容（前端渲染模式） - 消息ID: {message_id}, 角色: {role}")
        logger.info(f"  - 对话ID: {conversation_id}")
        logger.info(f"  - 内容长度: {len(content)} 字符")

        result = {
            "original_url": None,
            # 保留旧字段以兼容现有代码，但设置为None
            "rendered_html_url": None,
            "rendered_katex_url": None,
            "rendered_plain_url": None
        }

        try:
            # 只上传原始内容（JSON格式），前端负责渲染
            logger.info("📄 上传原始内容...")
            original_url = await self.upload_original_content(
                message_id, content, role, conversation_id,
                model_display_name, temperature, cost_info
            )
            result["original_url"] = original_url

            if original_url:
                logger.info(f"✅ 消息内容处理完成 - 原始内容URL: {original_url}")
            else:
                logger.error("❌ 原始内容上传失败")

            return result

        except Exception as e:
            logger.error(f"❌ 处理消息内容失败: {e}")
            logger.error(f"  - 消息ID: {message_id}")
            logger.error(f"  - 内容: {repr(content)}")
            import traceback
            logger.error(f"  - 堆栈: {traceback.format_exc()}")
            return result

    def format_content_for_model(self, content: str, supports_vision: bool = False) -> Any:
        """根据模型能力格式化消息内容

        Args:
            content: 原始消息内容
            supports_vision: 模型是否支持视觉功能

        Returns:
            格式化后的内容：
            - 文本模型：返回字符串（移除图片标记）
            - 可视模型：返回数组格式 [{"type": "text", "text": "..."}, {"type": "image_url", "image_url": {"url": "..."}}]
        """
        import re

        if not supports_vision:
            # 文本模型：移除图片标记，只保留文本
            text_content = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', content).strip()
            return text_content if text_content else content

        # 可视模型：构建数组格式
        try:
            # 提取图片URL
            image_urls = re.findall(r'!\[[^\]]*\]\((data:image[^)]+)\)', content)
            # 移除图片标记，保留文本
            text_only = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', content).strip()

            content_parts = []
            if text_only:
                content_parts.append({"type": "text", "text": text_only})

            for url in image_urls:
                content_parts.append({
                    "type": "image_url",
                    "image_url": {"url": url}
                })

            # 如果没有内容部分，至少返回一个空文本
            if not content_parts:
                content_parts.append({"type": "text", "text": content})

            return content_parts

        except Exception as e:
            logger.error(f"格式化可视消息内容失败: {e}")
            # 降级为文本格式
            return [{"type": "text", "text": content}]

    async def download_and_format_content(self, url: str, supports_vision: bool = False) -> Optional[Any]:
        """从URL下载内容并根据模型能力格式化

        Args:
            url: 内容URL
            supports_vision: 模型是否支持视觉功能

        Returns:
            格式化后的内容，适合发送给对应类型的模型
        """
        try:
            # 下载原始内容
            raw_content = await self.download_content_from_url(url)
            if raw_content is None:
                return None

            # 尝试解析JSON格式的内容
            try:
                import json
                content_data = json.loads(raw_content)
                if isinstance(content_data, dict) and "content" in content_data:
                    content = content_data["content"]
                else:
                    content = raw_content
            except (json.JSONDecodeError, TypeError):
                # 如果不是JSON格式，直接使用原始内容
                content = raw_content

            # 根据模型能力格式化内容
            return self.format_content_for_model(content, supports_vision)

        except Exception as e:
            logger.error(f"下载并格式化内容失败: {e}")
            return None

    async def download_content_from_url(self, url: str) -> Optional[str]:
        """从 MinIO URL 下载文本内容（HTML/JSON/TEXT），失败返回 None"""
        try:
            if not url or url.startswith("mock://") or url.startswith("placeholder://"):
                return None

            import urllib.parse, json
            parsed_url = urllib.parse.urlparse(url)

            # 如果是 http/https 预签名URL，直接通过HTTP获取文本内容
            if parsed_url.scheme in ("http", "https"):
                try:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        resp = await client.get(url, timeout=15.0)
                        if resp.status_code == 200:
                            text = resp.text
                            # 如果是 JSON，尝试提取 content 字段
                            if 'application/json' in (resp.headers.get('content-type') or '').lower():
                                try:
                                    data = resp.json()
                                    return data.get('content', text)
                                except Exception:
                                    # JSON解析失败，但URL表明这应该是JSON文件
                                    # 返回原始文本，但记录警告
                                    logger.warning(f"JSON文件解析失败，返回原始内容: {url}")
                                    return text
                            return text
                        else:
                            logger.warning(f"HTTP获取失败: {resp.status_code} {url}")
                except Exception as e:
                    logger.warning(f"HTTP获取异常: {e}")

            # 提取 bucket 与 object，尽量从URL中推断bucket，推断失败回退到默认bucket
            bucket = None
            object_name = None

            if parsed_url.scheme in ("minio", "s3"):
                # 例如: minio://bucket/path/to/object
                bucket = parsed_url.netloc or None
                object_name = parsed_url.path.lstrip('/')
            else:
                # http(s) 预签名：通常为 /<bucket>/<object>
                path_lstr = parsed_url.path.lstrip('/')
                if '/' in path_lstr:
                    bucket_candidate, rest = path_lstr.split('/', 1)
                    bucket = bucket_candidate or None
                    object_name = rest
                else:
                    object_name = path_lstr

                # 兼容此前把默认bucket拼到路径里的情况
                if object_name and object_name.startswith(f"{self.bucket_name}/"):
                    object_name = object_name[len(self.bucket_name)+1:]

            if not object_name:
                logger.warning(f"无法从URL中提取对象名称: {url}")
                return None

            use_bucket = bucket or self.bucket_name

            # 从 MinIO 获取对象
            response = self.client.get_object(use_bucket, object_name)
            raw = response.read()
            response.close()
            response.release_conn()
            content = raw.decode('utf-8', errors='ignore')

            # 如果是 JSON，尝试提取 content 字段
            if object_name.endswith('.json'):
                try:
                    data = json.loads(content)
                    return data.get('content', content)
                except Exception:
                    return content

            return content
        except Exception as e:
            logger.error(f"下载文本内容失败: {e}")
            return None

    async def download_bytes_from_url(self, url: str):
        """从 MinIO URL 下载原始字节内容，返回 (bytes, content_type) 或 (None, None)"""
        try:
            if not url or url.startswith("mock://") or url.startswith("placeholder://"):
                return None, None
            import urllib.parse
            parsed_url = urllib.parse.urlparse(url)

            # 如果是 http/https 预签名URL，直接通过HTTP获取字节内容
            if parsed_url.scheme in ("http", "https"):
                try:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        resp = await client.get(url, timeout=15.0)
                        if resp.status_code == 200:
                            data = resp.content
                            content_type = resp.headers.get('content-type') or 'application/octet-stream'
                            return data, content_type
                        else:
                            logger.warning(f"HTTP获取失败: {resp.status_code} {url}")
                except Exception as e:
                    logger.warning(f"HTTP获取异常: {e}")

            # 提取 bucket 与 object
            bucket = None
            object_name = None
            if parsed_url.scheme in ("minio", "s3"):
                bucket = parsed_url.netloc or None
                object_name = parsed_url.path.lstrip('/')
            else:
                path_lstr = parsed_url.path.lstrip('/')
                if '/' in path_lstr:
                    bucket_candidate, rest = path_lstr.split('/', 1)
                    bucket = bucket_candidate or None
                    object_name = rest
                else:
                    object_name = path_lstr

                if object_name and object_name.startswith(f"{self.bucket_name}/"):
                    object_name = object_name[len(self.bucket_name)+1:]

            if not object_name:
                logger.warning(f"无法从URL中提取对象名称: {url}")
                return None, None

            use_bucket = bucket or self.bucket_name

            response = self.client.get_object(use_bucket, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            content_type = "application/octet-stream"
            lower = object_name.lower()
            if lower.endswith('.png'):
                content_type = 'image/png'
            elif lower.endswith('.jpg') or lower.endswith('.jpeg'):
                content_type = 'image/jpeg'
            elif lower.endswith('.gif'):
                content_type = 'image/gif'
            elif lower.endswith('.webp'):
                content_type = 'image/webp'
            elif lower.endswith('.svg'):
                content_type = 'image/svg+xml'
            elif lower.endswith('.html'):
                content_type = 'text/html; charset=utf-8'
            elif lower.endswith('.json'):
                content_type = 'application/json; charset=utf-8'
            elif lower.endswith('.txt'):
                content_type = 'text/plain; charset=utf-8'
            return data, content_type
        except Exception as e:
            logger.error(f"下载字节内容失败: {e}")
            return None, None
    async def upload_image(self, message_id: int, image_bytes: bytes, filename: Optional[str] = None, mime_type: Optional[str] = None) -> Optional[str]:
        try:
            # 推断扩展名
            ext = ''
            if filename and '.' in filename:
                ext = filename[filename.rfind('.'):]
            else:
                # 尝试从 mime 推断
                if mime_type == 'image/png':
                    ext = '.png'
                elif mime_type in ['image/jpeg', 'image/jpg']:
                    ext = '.jpg'
                elif mime_type == 'image/gif':
                    ext = '.gif'
                elif mime_type == 'image/webp':
                    ext = '.webp'
                else:
                    ext = '.bin'
            object_name = self._generate_object_name(message_id, 'attachment', ext)
            try:
                self._ensure_bucket_exists()
            except Exception:
                pass
            ct = mime_type or 'application/octet-stream'
            result = self.client.put_object(
                self.bucket_name,
                object_name,
                io.BytesIO(image_bytes),
                len(image_bytes),
                content_type=ct
            )
            url = self._build_object_http_url(object_name)
            logger.info(f"✅ 图片上传成功: {url}")
            return url
        except Exception as e:
            logger.error(f"❌ 图片上传失败: {e}")
            return None



    async def delete_message_content(self, message_id: int) -> bool:
        """删除消息的所有内容文件"""
        try:
            # 列出消息相关的所有对象
            objects = self.client.list_objects(
                self.bucket_name,
                prefix=f"messages/{message_id}/",
                recursive=True
            )

            # 删除所有对象
            for obj in objects:
                self.client.remove_object(self.bucket_name, obj.object_name)
                logger.info(f"删除对象: {obj.object_name}")

            return True

        except Exception as e:
            logger.error(f"删除消息内容失败: {e}")
            return False

# 创建全局MinIO服务实例
minio_service = MinIOService()
