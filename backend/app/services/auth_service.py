# app/services/auth_service.py - 认证服务

from jose import jwt
import httpx
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status

from ..core.config import settings
from ..models.schemas import UserInfo

logger = logging.getLogger(__name__)

class AuthService:
    """认证服务类"""
    
    def __init__(self):
        self.record_api_url = settings.RECORD_API_BASE_URL
        self.jwt_secret = settings.JWT_SECRET_KEY
        self.jwt_algorithm = settings.JWT_ALGORITHM
        self.jwt_expire_hours = settings.JWT_EXPIRE_HOURS
    
    async def verify_api_key(self, api_key: str) -> Optional[UserInfo]:
        """验证API密钥并获取用户信息"""

        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {api_key}"}
                response = await client.get(
                    f"{self.record_api_url}/api/v1/user/profile",
                    headers=headers,
                    timeout=10.0
                )

                if response.status_code == 200:
                    user_data = response.json()
                    logger.info(f"API密钥验证成功: 用户ID {user_data.get('id')}")
                    return UserInfo(**user_data)
                elif response.status_code == 401:
                    logger.warning(f"API密钥验证失败: 无效的密钥")
                    return None
                else:
                    logger.error(f"API密钥验证失败: HTTP {response.status_code}")
                    return None

        except httpx.TimeoutException:
            logger.error("API密钥验证超时")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="认证服务暂时不可用"
            )
        except Exception as e:
            logger.error(f"API密钥验证异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="认证服务内部错误"
            )
    
    def create_access_token(self, user_id: int, api_key: str) -> str:
        """创建JWT访问令牌"""
        expire = datetime.utcnow() + timedelta(hours=self.jwt_expire_hours)
        
        payload = {
            "sub": str(user_id),
            "api_key": api_key,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
        logger.info(f"为用户 {user_id} 创建访问令牌")
        return token
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # 检查令牌类型
            if payload.get("type") != "access":
                logger.warning("无效的令牌类型")
                return None
            
            # 检查过期时间
            exp = payload.get("exp")
            if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
                logger.warning("令牌已过期")
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("令牌已过期")
            return None
        except jwt.JWTError as e:
            logger.warning(f"无效的令牌: {e}")
            return None
        except Exception as e:
            logger.error(f"令牌验证异常: {e}")
            return None
    
    async def get_current_user(self, token: str) -> Optional[UserInfo]:
        """根据令牌获取当前用户信息"""
        payload = self.verify_token(token)
        if not payload:
            return None
        
        api_key = payload.get("api_key")
        if not api_key:
            logger.warning("令牌中缺少API密钥")
            return None
        
        # 重新验证API密钥（确保用户状态最新）
        return await self.verify_api_key(api_key)
    
    def refresh_token(self, token: str) -> Optional[str]:
        """刷新访问令牌"""
        payload = self.verify_token(token)
        if not payload:
            return None
        
        user_id = payload.get("sub")
        api_key = payload.get("api_key")
        
        if not user_id or not api_key:
            return None
        
        # 创建新的令牌
        return self.create_access_token(int(user_id), api_key)

# 创建全局认证服务实例
auth_service = AuthService()
