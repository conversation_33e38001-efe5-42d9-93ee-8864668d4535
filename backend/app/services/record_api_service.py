# app/services/record_api_service.py - Record API 服务类

import logging
import httpx
from typing import Optional, Dict, Any, List
from ..core.config import settings

logger = logging.getLogger(__name__)

class RecordAPIService:
    """Record API 服务类，封装所有与 Record API 的交互"""
    
    def __init__(self):
        self.base_url = settings.RECORD_API_BASE_URL.rstrip('/')
        self.admin_api_key = settings.RECORD_API_KEY
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[Any, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        api_key: Optional[str] = None,
        timeout: float = 10.0
    ) -> Optional[httpx.Response]:
        """发送请求到 Record API"""
        try:
            headers = {"Authorization": f"Bearer {api_key or self.admin_api_key}"}
            url = f"{self.base_url}{endpoint}"

            logger.info(f"Record API 请求: {method} {url}")
            logger.debug(f"使用 API 密钥: {(api_key or self.admin_api_key)[:20]}...")

            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params, timeout=timeout)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data, timeout=timeout)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data, timeout=timeout)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, timeout=timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                if response.status_code == 401:
                    logger.error(f"Record API 认证失败: API 密钥无效")
                elif response.status_code >= 400:
                    logger.error(f"Record API 请求失败: HTTP {response.status_code} - {response.text}")

                return response

        except Exception as e:
            logger.error(f"Record API 请求异常: {method} {endpoint} - {e}")
            return None
    
    # ===== 用户管理 API =====
    
    async def get_user_profile(self, user_api_key: str) -> Optional[Dict[str, Any]]:
        """获取用户个人信息"""
        response = await self._make_request("GET", "/api/v1/user/profile", api_key=user_api_key)
        if response and response.status_code == 200:
            return response.json()
        return None
    
    async def update_user_profile(self, user_api_key: str, profile_data: Dict[str, Any]) -> bool:
        """更新用户个人信息"""
        response = await self._make_request("PUT", "/api/v1/user/profile", data=profile_data, api_key=user_api_key)
        return response and response.status_code == 200
    
    async def get_user_billing(self, user_api_key: str) -> Optional[List[Dict[str, Any]]]:
        """获取用户计费记录"""
        response = await self._make_request("GET", "/api/v1/user/billing", api_key=user_api_key)
        if response and response.status_code == 200:
            return response.json()
        return None
        
    # ===== 对话管理 API =====
    
    async def get_conversations(self, user_id: Optional[int] = None, api_key: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """获取对话列表"""
        params = {"page": 1, "size": 100}  # 获取更多对话
        response = await self._make_request("GET", "/api/v1/user/conversations/", params=params, api_key=api_key)
        if response and response.status_code == 200:
            return response.json()
        return None

    async def create_conversation(self, conversation_data: Dict[str, Any], api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """创建新对话"""
        response = await self._make_request("POST", "/api/v1/user/conversations/", data=conversation_data, api_key=api_key)
        if response and response.status_code in [200, 201]:
            return response.json()
        return None

    async def get_conversation(self, conversation_id: int, api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取对话详情"""
        response = await self._make_request("GET", f"/api/v1/user/conversations/{conversation_id}", api_key=api_key)
        if response and response.status_code == 200:
            return response.json()
        return None

    async def update_conversation(self, conversation_id: int, conversation_data: Dict[str, Any], api_key: Optional[str] = None) -> bool:
        """更新对话"""
        response = await self._make_request("PUT", f"/api/v1/user/conversations/{conversation_id}", data=conversation_data, api_key=api_key)
        return response and response.status_code == 200

    async def delete_conversation(self, conversation_id: int, api_key: Optional[str] = None) -> bool:
        """删除对话"""
        response = await self._make_request("DELETE", f"/api/v1/user/conversations/{conversation_id}", api_key=api_key)
        return response and response.status_code == 200
    
    # ===== 消息管理 API =====
    
    async def get_messages(self, conversation_id: Optional[int] = None, api_key: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """获取消息列表"""
        params = {"page": 1, "size": 200}
        if conversation_id:
            params["conversation_id"] = conversation_id

        logger.info(f"获取消息列表: conversation_id={conversation_id}, api_key={api_key[:20] if api_key else 'None'}...")
        response = await self._make_request("GET", "/api/v1/user/messages/", params=params, api_key=api_key)

        if response and response.status_code == 200:
            result = response.json()
            logger.info(f"成功获取 {len(result)} 条消息")
            return result
        elif response:
            logger.error(f"获取消息失败: HTTP {response.status_code} - {response.text}")
        else:
            logger.error("获取消息失败: 无响应")
        return None

    async def create_message(self, message_data: Dict[str, Any], api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """创建新消息"""
        response = await self._make_request("POST", "/api/v1/user/messages/", data=message_data, api_key=api_key)
        if response and response.status_code in [200, 201]:
            return response.json()
        return None

    async def get_message(self, message_id: int, api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取消息详情"""
        response = await self._make_request("GET", f"/api/v1/user/messages/{message_id}", api_key=api_key)
        if response and response.status_code == 200:
            return response.json()
        return None

    async def update_message(self, message_id: int, message_data: Dict[str, Any], api_key: Optional[str] = None) -> bool:
        """更新消息"""
        response = await self._make_request("PUT", f"/api/v1/user/messages/{message_id}", data=message_data, api_key=api_key)
        return response and response.status_code == 200

    async def delete_message(self, message_id: int, api_key: Optional[str] = None) -> bool:
        """删除消息"""
        response = await self._make_request("DELETE", f"/api/v1/user/messages/{message_id}", api_key=api_key)
        return response and response.status_code == 200

    async def create_message_cost(self, message_id: int, cost_data: Dict[str, Any], api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """创建消息费用记录"""
        response = await self._make_request("POST", f"/api/v1/user/messages/{message_id}/billing", data=cost_data, api_key=api_key)
        if response and response.status_code in [200, 201]:
            return response.json()
        return None

    async def update_message_cost(self, message_id: int, cost_data: Dict[str, Any], api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """更新消息费用记录"""
        response = await self._make_request("PUT", f"/api/v1/user/messages/{message_id}/cost", data=cost_data, api_key=api_key)
        if response and response.status_code == 200:
            return response.json()
        return None

# 创建全局实例
record_api_service = RecordAPIService()
