# app/api/chat.py - 聊天API路由

import logging
import httpx
from datetime import datetime
from fastapi import APIRouter, HTTPException, status, Depends, WebSocket, WebSocketDisconnect
from typing import Dict, Any

from ..models.schemas import ChatRequest, ChatResponse, BaseResponse
from ..models.schemas import TempImageU<PERSON>loadRequest, TempImageUploadResponse
from ..api.auth import get_current_user_dependency
from ..services.chat_service import chat_service
from ..core.config import settings
from ..services.record_api_service import record_api_service

async def _get_model_pricing(model_name: str) -> dict:
    """
    返回按“每token”计价的 dict: { input: float, output: float }
    兼容多种上游字段：
    - pricing.input_token / pricing.output_token（可能每token或每千token）
    - input_cost_per_token / output_cost_per_token（每token）
    - input_cost_per_1k_tokens / output_cost_per_1k_tokens（每千token）
    - pricing.input_per_1k / pricing.output_per_1k（每千token）
    """
    try:
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
            resp = await client.get(f"{settings.MODEL_API_BASE_URL}/api/v1/models", headers=headers, timeout=10.0)
            if resp.status_code != 200:
                return {}
            data = resp.json()
            models = data.get("data") or data.get("models") or (data if isinstance(data, list) else [])
            for m in models:
                mid = (m.get("id") or m.get("name") or "").strip()
                disp = (m.get("display_name") or "").strip()
                target = (str(model_name) or "").strip()
                if target and (target.lower() == mid.lower() or target.lower() == disp.lower()):
                    raw_pricing = m.get("pricing") or {}
                    # 优先读取 *_token_price 并结合 unit 转换
                    unit = (raw_pricing.get("unit") or "").lower()
                    in_price = raw_pricing.get("input_token_price")
                    out_price = raw_pricing.get("output_token_price")
                    def to_float(x):
                        try:
                            return float(x)
                        except Exception:
                            return None
                    in_val = to_float(in_price)
                    out_val = to_float(out_price)

                    # 根据单位转换为每token价格
                    if in_val is not None:
                        if 'per_1m' in unit or 'per_1000k' in unit:
                            # 每百万token -> 每token
                            in_val = in_val / 1000000.0
                        elif 'per_1k' in unit:
                            # 每千token -> 每token
                            in_val = in_val / 1000.0

                    if out_val is not None:
                        if 'per_1m' in unit or 'per_1000k' in unit:
                            # 每百万token -> 每token
                            out_val = out_val / 1000000.0
                        elif 'per_1k' in unit:
                            # 每千token -> 每token
                            out_val = out_val / 1000.0

                    # 兜底：尝试其他常见键（注意：这些值可能需要单位转换）
                    if in_val is None:
                        fallback_val = to_float(raw_pricing.get("input_token") or raw_pricing.get("input"))
                        if fallback_val is not None:
                            if 'per_1m' in unit or 'per_1000k' in unit:
                                in_val = fallback_val / 1000000.0
                            elif 'per_1k' in unit:
                                in_val = fallback_val / 1000.0
                            else:
                                in_val = fallback_val
                    if out_val is None:
                        fallback_val = to_float(raw_pricing.get("output_token") or raw_pricing.get("output"))
                        if fallback_val is not None:
                            if 'per_1m' in unit or 'per_1000k' in unit:
                                out_val = fallback_val / 1000000.0
                            elif 'per_1k' in unit:
                                out_val = fallback_val / 1000.0
                            else:
                                out_val = fallback_val
                    if (in_val is None) and to_float(m.get("input_cost_per_token")) is not None:
                        in_val = to_float(m.get("input_cost_per_token"))  # 已经是每token价格
                    if (out_val is None) and to_float(m.get("output_cost_per_token")) is not None:
                        out_val = to_float(m.get("output_cost_per_token"))  # 已经是每token价格
                    if (in_val is None) and to_float(m.get("input_cost_per_1k_tokens")) is not None:
                        in_val = to_float(m.get("input_cost_per_1k_tokens")) / 1000.0  # 每千token -> 每token
                    if (out_val is None) and to_float(m.get("output_cost_per_1k_tokens")) is not None:
                        out_val = to_float(m.get("output_cost_per_1k_tokens")) / 1000.0  # 每千token -> 每token

                    def normalize(val, key_hint: str) -> float | None:
                        if val is None:
                            return None
                        try:
                            v = float(val)
                        except Exception:
                            return None
                        # 若字段名暗示“每千token”，则换算为每token
                        if "per_1k" in key_hint or "per1000" in key_hint or "1k" in key_hint:
                            return v / 1000.0
                        return v

                    # 逐个候选尝试并根据key判断是否需要/1k
                    in_val = None
                    for key in [
                        "pricing.input_token","pricing.input","model.input_cost_per_token",
                        "pricing.input_cost_per_token","pricing.input_per_1k",
                        "model.input_cost_per_1k_tokens","pricing.input_cost_per_1k_tokens"
                    ]:
                        src = (
                            raw_pricing.get(key.split(".")[-1]) if key.startswith("pricing.")
                            else m.get(key.split(".")[-1])
                        )
                        in_val = normalize(src, key)
                        if in_val is not None:
                            break

                    out_val = None
                    for key in [
                        "pricing.output_token","pricing.output","model.output_cost_per_token",
                        "pricing.output_cost_per_token","pricing.output_per_1k",
                        "model.output_cost_per_1k_tokens","pricing.output_cost_per_1k_tokens"
                    ]:
                        src = (
                            raw_pricing.get(key.split(".")[-1]) if key.startswith("pricing.")
                            else m.get(key.split(".")[-1])
                        )
                        out_val = normalize(src, key)
                        if out_val is not None:
                            break

                    # 兜底：从 pricing 字段名里猜测 input/output 并识别数量级
                    def scan(raw: dict, kinds: tuple[str, ...]) -> float | None:
                        def to_float(x):
                            try:
                                if isinstance(x, str):
                                    s = x.strip().lower().replace('$', '')
                                    s = s.replace('/1k', '').replace('/k', '').replace('/1000', '')
                                    s = s.replace('/1m', '').replace('/m', '').replace('/1000000', '')
                                    return float(s)
                                return float(x)
                            except Exception:
                                return None
                        best = None
                        for k, v in raw.items():
                            kl = str(k).lower()
                            if any(tag in kl for tag in kinds):
                                fv = to_float(v)
                                if fv is None:
                                    continue
                                scale = 1.0
                                if 'per_1k' in kl or 'per1000' in kl or '1k' in kl or '/1k' in kl or '/1000' in kl:
                                    scale = 1/1000.0
                                elif 'per_1m' in kl or 'per_million' in kl or '1m' in kl or 'million' in kl:
                                    scale = 1/1_000_000.0
                                val = fv * scale
                                best = val
                                break
                        return best

                    # 只有在值为None时才使用scan函数，避免覆盖已转换的值
                    if in_val is None:
                        in_val = scan(raw_pricing, ("input", "prompt"))
                    if out_val is None:
                        out_val = scan(raw_pricing, ("output", "completion"))

                    # scan函数可能没有考虑全局unit，需要根据unit进行后处理
                    if unit and in_val is not None:
                        if 'per_1m' in unit or 'per_1000k' in unit:
                            in_val = in_val / 1000000.0
                        elif 'per_1k' in unit:
                            in_val = in_val / 1000.0

                    if unit and out_val is not None:
                        if 'per_1m' in unit or 'per_1000k' in unit:
                            out_val = out_val / 1000000.0
                        elif 'per_1k' in unit:
                            out_val = out_val / 1000.0

                    logger.info(f"模型 {model_name} 计费解析: unit={unit}, raw={raw_pricing} -> per_token in={in_val}, out={out_val}")
                    return {"input": in_val, "output": out_val}
    except Exception as e:
        logger.warning(f"获取模型单价失败: {e}")
    return {}

async def _create_record_api_conversation(conversation_id: int, api_key: str = None) -> int:
    """
    在Record API中确保对话记录存在（先检查是否存在，不存在才创建）

    Args:
        conversation_id: 对话ID
        api_key: 用户API密钥

    Returns:
        int: Record API中的对话ID，失败返回None
    """
    try:
        # 先检查Record API中是否已存在该对话
        existing_conversation = await record_api_service.get_conversation(conversation_id, api_key=api_key)
        
        if existing_conversation:
            logger.info(f"Record API中已存在对话: ID={conversation_id}")
            return existing_conversation.get('id', conversation_id)
        
        # 如果不存在，才创建新对话
        logger.info(f"Record API中不存在对话 {conversation_id}，创建新对话...")
        conversation_data = {
            "user_id": 1,  # 使用默认用户ID
            "title": f"对话 {conversation_id}",
            "model_id": 1,  # 使用默认模型ID
            "temperature": 0.7,
            "max_tokens": None
        }

        # 使用新的 Record API 服务
        result = await record_api_service.create_conversation(conversation_data, api_key=api_key)

        if result:
            created_conversation_id = result.get('id')
            logger.info(f"Record API对话记录创建成功: 请求ID={conversation_id}, 创建ID={created_conversation_id}")
            return created_conversation_id or conversation_id
        else:
            logger.warning("Record API对话记录创建失败")
            return None

    except Exception as e:
        logger.error(f"确保Record API对话记录存在时异常: {e}")
        return None

async def _create_record_api_message(message_id: int, conversation_id: int, content_url: str) -> int:
    """
    在Record API中创建assistant消息记录

    Args:
        message_id: assistant消息ID（用于日志）
        conversation_id: 对话ID
        content_url: 消息内容URL

    Returns:
        int: Record API中创建的消息ID，失败返回None
    """
    try:
        message_data = {
            "conversation_id": conversation_id,
            "role": "assistant",
            "content_url": content_url,
            "plain_html_url": content_url  # 使用相同的URL
        }

        # 使用新的 Record API 服务
        result = await record_api_service.create_message(message_data)

        if result:
            record_message_id = result.get('id')
            logger.info(f"Record API消息记录创建成功: 本地message_id={message_id}, Record API message_id={record_message_id}")
            return record_message_id
        else:
            logger.warning("Record API消息记录创建失败")
            return None

    except Exception as e:
        logger.error(f"创建Record API消息记录异常: {e}")
        return None

async def _create_message_cost(record_message_id: int, usage: dict, pricing: dict, user_id: int, conversation_id: int, model_name: str, api_key: str) -> bool:
    """
    为assistant消息创建费用记录，Records API会自动扣减用户余额

    Args:
        record_message_id: Record API中的消息ID
        usage: token使用量信息 {"prompt_tokens": int, "completion_tokens": int}
        pricing: 模型定价信息 {"input": float, "output": float} (每token价格)

    Returns:
        bool: 是否成功创建费用记录
    """
    if not usage or not pricing:
        logger.info("没有usage或pricing信息，跳过计费")
        return True

    try:
        # 提取token数量
        prompt_tokens = int(usage.get("prompt_tokens") or usage.get("input_tokens") or 0)
        completion_tokens = int(usage.get("completion_tokens") or usage.get("output_tokens") or 0)

        if prompt_tokens <= 0 and completion_tokens <= 0:
            logger.info("token数量为0，跳过计费")
            return True

        # 计算费用
        input_price_per_token = float(pricing.get("input") or 0)
        output_price_per_token = float(pricing.get("output") or 0)

        prompt_cost = prompt_tokens * input_price_per_token
        completion_cost = completion_tokens * output_price_per_token
        total_cost = prompt_cost + completion_cost

        # 即使费用为0也要调用Record API（用于统计和测试）
        if total_cost <= 0:
            logger.info(f"总费用为0，但仍然调用Record API进行统计: record_message_id={record_message_id}")
            # 继续执行，不跳过

        # 构建费用记录数据（按照Record API接口格式）
        cost_data = {
            "message_id": record_message_id,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_cost": f"{total_cost:.6f}"  # 转换为字符串格式，与测试保持一致
        }

        logger.info(f"创建费用记录: record_message_id={record_message_id}, prompt_tokens={prompt_tokens}, "
                   f"completion_tokens={completion_tokens}, total_cost={total_cost:.6f}")

        # 调用Record API创建计费记录
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {api_key}"}

            # 构建计费数据（按照Record API格式）
            billing_data = {
                "user_id": user_id,
                "message_id": record_message_id,
                "conversation_id": conversation_id,
                "billing_type": "message",
                "model_name": model_name,
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "prompt_cost": prompt_cost,
                "completion_cost": completion_cost,
                "total_cost": total_cost,
                "description": "AI聊天费用"
            }

            response = await client.post(
                f"{settings.RECORD_API_BASE_URL}/api/v1/user/messages/{record_message_id}/billing",
                headers=headers,
                json=billing_data,
                timeout=10.0
            )

            if response.status_code in [200, 201]:
                logger.info(f"💰 费用记录创建成功: record_message_id={record_message_id}, total_cost={total_cost:.6f}")
                return True
            else:
                logger.error(f"费用记录创建失败: HTTP {response.status_code} - {response.text}")
                return False

    except ValueError as e:
        logger.error(f"费用计算数据类型错误: {e}")
        return False
    except Exception as e:
        logger.error(f"创建费用记录异常: {e}")
        return False

logger = logging.getLogger(__name__)
# 简单内存缓存临时图片（仅示例用，生产可换成Redis/DB）
TEMP_IMAGES: Dict[int, Dict[str, Any]] = {}
TEMP_IMG_ID = 1000000

router = APIRouter()

from base64 import b64decode
from ..models.schemas import ImageUploadRequest, ImageUploadResponse
from ..services.minio_service import minio_service

@router.post("/", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user = Depends(get_current_user_dependency)
):
    """
    发送聊天消息并获取AI回复
    """
    try:
        # 0. 首先检查对话是否存在
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {current_user.api_key}"}
                response = await client.get(
                    f"{settings.RECORD_API_BASE_URL}/api/v1/user/conversations/{chat_request.conversation_id}",
                    headers=headers,
                    timeout=10.0
                )
                if response.status_code == 404:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="对话不存在"
                    )
                elif response.status_code != 200:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="无法验证对话状态"
                    )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"验证对话状态失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="验证对话状态失败"
            )

        # 1. 处理附件：将临时图片以base64内联到消息末尾（按你的方案）
        content_to_send = chat_request.message
        if getattr(chat_request, 'attachments', None):
            for aid in chat_request.attachments:
                info = TEMP_IMAGES.get(aid)
                if not info:
                    continue
                b64 = info['b64']
                mime = info.get('mime') or 'image/png'
                content_to_send += f"\n\n![uploaded image](data:{mime};base64,{b64})"

        # 2. 创建用户消息
        user_message = await chat_service.create_user_message(
            api_key=current_user.api_key,
            conversation_id=chat_request.conversation_id,
            content=content_to_send,
            model_name=chat_request.model_name
        )

        if not user_message:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建用户消息失败"
            )

        user_message_id = user_message.get('id')
        logger.info(f"用户消息创建成功: ID={user_message_id}")

        # 2. 生成AI回复
        model_name = chat_request.model_name or settings.DEFAULT_MODEL
        temperature = chat_request.temperature or settings.DEFAULT_TEMPERATURE
        # 只有在明确指定时才使用max_tokens限制，否则让模型自由输出
        max_tokens = chat_request.max_tokens if chat_request.max_tokens is not None else settings.MAX_TOKENS

        ai_result = await chat_service.generate_ai_response(
            conversation_id=chat_request.conversation_id,
            model_name=model_name,
            user_message=content_to_send,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=chat_request.stream,
            api_key=current_user.api_key
        )

        usage = None
        if isinstance(ai_result, dict):
            ai_response_text = ai_result.get("content") or ""
            usage = ai_result.get("usage")
        else:
            ai_response_text = ai_result or ""

        if not ai_response_text:
            ai_response_text = "抱歉，我无法生成回复，请稍后重试。"

        # 计算费用信息
        cost_info = None
        if usage:
            try:
                pricing = await _get_model_pricing(model_name)
                if pricing.get("input") is not None and pricing.get("output") is not None:
                    prompt_tokens = int(usage.get("prompt_tokens") or usage.get("input_tokens") or 0)
                    completion_tokens = int(usage.get("completion_tokens") or usage.get("output_tokens") or 0)

                    input_price_per_token = float(pricing.get("input") or 0)
                    output_price_per_token = float(pricing.get("output") or 0)

                    prompt_cost = prompt_tokens * input_price_per_token
                    completion_cost = completion_tokens * output_price_per_token
                    total_cost = prompt_cost + completion_cost

                    cost_info = {
                        "prompt_tokens": prompt_tokens,
                        "completion_tokens": completion_tokens,
                        "total_tokens": prompt_tokens + completion_tokens,
                        "prompt_cost": prompt_cost,
                        "completion_cost": completion_cost,
                        "total_cost": total_cost
                    }
            except Exception as e:
                logger.warning(f"计算费用信息失败: {e}")

        # 3. 创建AI消息
        ai_message = await chat_service.create_ai_message(
            api_key=current_user.api_key,
            conversation_id=chat_request.conversation_id,
            content=ai_response_text,
            model_name=model_name,
            is_error="抱歉" in ai_response_text or "错误" in ai_response_text,
            temperature=temperature,
            cost_info=cost_info
        )

        ai_message_id = ai_message.get('id') if ai_message else None

        logger.info(f"聊天完成: 用户消息={user_message_id}, AI消息={ai_message_id}")

        # 创建费用记录（最佳努力）
        try:
            if usage and ai_message_id:
                pricing = await _get_model_pricing(model_name)
                logger.info(f"获取到模型 {model_name} 的定价信息: {pricing}")

                # 即使定价为0也要尝试计费（用于统计和测试）
                if pricing and ("input" in pricing or "output" in pricing):
                    # AI消息已经在chat_service.create_ai_message中创建，直接使用该消息ID进行计费
                    logger.info(f"使用已创建的AI消息进行计费: message_id={ai_message_id}")
                    
                    # 计算费用详情
                    prompt_tokens = int(usage.get("prompt_tokens") or usage.get("input_tokens") or 0)
                    completion_tokens = int(usage.get("completion_tokens") or usage.get("output_tokens") or 0)
                    
                    input_price_per_token = float(pricing.get("input") or 0)
                    output_price_per_token = float(pricing.get("output") or 0)
                    
                    prompt_cost = prompt_tokens * input_price_per_token
                    completion_cost = completion_tokens * output_price_per_token
                    total_cost = prompt_cost + completion_cost
                    
                    # 使用Record API格式创建计费记录
                    billing_data = {
                        "user_id": current_user.id,
                        "message_id": ai_message_id,
                        "conversation_id": chat_request.conversation_id,
                        "billing_type": "message",
                        "model_name": model_name,
                        "prompt_tokens": prompt_tokens,
                        "completion_tokens": completion_tokens,
                        "prompt_cost": prompt_cost,
                        "completion_cost": completion_cost,
                        "total_cost": total_cost,
                        "description": "AI聊天费用"
                    }
                    
                    logger.info(f"创建计费记录: message_id={ai_message_id}, total_cost={total_cost:.6f}")
                    
                    cost_created = await record_api_service.create_message_cost(
                        ai_message_id, billing_data, api_key=current_user.api_key
                    )
                    logger.info(f"费用记录创建结果: {cost_created}")
                    if cost_created:
                        logger.info(f"💰 费用扣除成功: AI消息={ai_message_id}")
                    else:
                        logger.warning(f"费用记录创建失败，但聊天继续进行")
                else:
                    logger.warning(f"无法获取模型 {model_name} 的定价信息，跳过计费")
            else:
                logger.info("没有usage信息或AI消息ID，跳过计费")
        except Exception as e:
            logger.warning(f"费用记录创建过程异常: {e}")

        # 获取完整的消息信息（包括MinIO URLs）
        user_message_info = None
        assistant_message_info = None

        if user_message_id:
            try:
                # 获取用户消息的完整信息
                messages = await chat_service.get_conversation_messages(
                    conversation_id=chat_request.conversation_id,
                    user_mathjax_setting=current_user.mathjax,
                    limit=100
                )
                # 找到对应的用户消息
                for msg in messages:
                    if msg.get('id') == user_message_id:
                        user_message_info = msg
                    elif msg.get('id') == ai_message_id:
                        assistant_message_info = msg
            except Exception as e:
                logger.warning(f"获取消息详细信息失败: {e}")

        # 使用完临时附件后清理后端暂存
        try:
            if getattr(chat_request, 'attachments', None):
                removed = []
                for aid in chat_request.attachments:
                    if TEMP_IMAGES.pop(aid, None) is not None:
                        removed.append(aid)
                if removed:
                    logger.info(f"已清理临时图片: {removed}")
        except Exception as cleanup_err:
            logger.warning(f"清理临时图片失败: {cleanup_err}")

        return ChatResponse(
            conversation_id=chat_request.conversation_id,
            user_message_id=user_message_id,
            assistant_message_id=ai_message_id,
            response=ai_response_text,
            message="聊天完成",
            user_message=user_message_info,
            assistant_message=assistant_message_info,
            usage=usage,
            cost=None  # 费用信息现在在Records API中管理
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="聊天服务内部错误"
        )

@router.websocket("/ws/{conversation_id}")
async def websocket_chat(
    websocket: WebSocket,
    conversation_id: int,
    token: str = None
):
    """
    WebSocket聊天接口（流式响应）
    """
    await websocket.accept()

    try:
        # 验证用户身份
        if not token:
            await websocket.send_json({
                "type": "error",
                "message": "缺少认证令牌"
            })
            await websocket.close()
            return

        # 这里应该验证token并获取用户信息
        # user = await auth_service.get_current_user(token)
        # if not user:
        #     await websocket.send_json({
        #         "type": "error",
        #         "message": "认证失败"
        #     })
        #     await websocket.close()
        #     return

        logger.info(f"WebSocket连接建立: 对话={conversation_id}")

        while True:
            # 接收客户端消息
            data = await websocket.receive_json()
            message_type = data.get("type")

            if message_type == "chat":
                # 处理聊天消息
                message = data.get("message", "")
                model_name = data.get("model_name", settings.DEFAULT_MODEL)

                if not message.strip():
                    await websocket.send_json({
                        "type": "error",
                        "message": "消息内容不能为空"
                    })
                    continue

                # 发送开始信号
                await websocket.send_json({
                    "type": "chat_start",
                    "message": "开始生成回复..."
                })

                try:
                    # 这里应该实现流式聊天
                    # 暂时返回简单回复
                    response = f"收到您的消息：{message}"

                    # 发送回复内容
                    await websocket.send_json({
                        "type": "chat_chunk",
                        "content": response
                    })

                    # 发送结束信号
                    await websocket.send_json({
                        "type": "chat_end",
                        "message": "回复完成"
                    })

                except Exception as e:
                    logger.error(f"WebSocket聊天异常: {e}")
                    await websocket.send_json({
                        "type": "chat_error",
                        "message": f"生成回复时出错: {str(e)}"
                    })

            elif message_type == "ping":
                # 心跳检测
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": data.get("timestamp")
                })

            else:
                await websocket.send_json({
                    "type": "error",
                    "message": f"未知的消息类型: {message_type}"
                })

    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: 对话={conversation_id}")
    except Exception as e:
        logger.error(f"WebSocket异常: {e}")
        try:
            await websocket.send_json({
                "type": "error",
                "message": "服务器内部错误"
            })
        except:
            pass

@router.get("/history/{conversation_id}")
async def get_chat_history(
    conversation_id: int,
    limit: int = 20,
    current_user = Depends(get_current_user_dependency)
):
    """
    获取聊天历史（用于AI聊天的简化格式）
    """
    try:
        history = await chat_service.get_conversation_history(
            api_key=current_user.api_key,
            conversation_id=conversation_id,
            limit=limit
        )

        return {
            "success": True,
            "conversation_id": conversation_id,
            "history": history,
            "message": f"获取到 {len(history)} 条历史消息"
        }

    except Exception as e:
        logger.error(f"获取聊天历史异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天历史服务内部错误"
        )

@router.get("/messages/{conversation_id}")
async def get_chat_messages(
    conversation_id: int,
    limit: int = 50,
    current_user = Depends(get_current_user_dependency)
):
    """
    获取聊天消息（用于前端显示的完整格式）
    """
    try:
        messages = await chat_service.get_conversation_messages(
            conversation_id=conversation_id,
            user_mathjax_setting=current_user.mathjax,
            limit=limit,
            auth_api_key=current_user.api_key,
        )

        return {
            "success": True,
            "conversation_id": conversation_id,
            "messages": messages,
            "message": f"获取到 {len(messages)} 条消息"
        }

    except Exception as e:
        logger.error(f"获取聊天消息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天消息服务内部错误"
        )

@router.post("/regenerate")
async def regenerate_response(
    chat_request: ChatRequest,
    current_user = Depends(get_current_user_dependency)
):
    """
    重新生成AI回复
    """
    try:
        # 生成新的AI回复
        model_name = chat_request.model_name or settings.DEFAULT_MODEL
        temperature = chat_request.temperature or settings.DEFAULT_TEMPERATURE
        # 只有在明确指定时才使用max_tokens限制，否则让模型自由输出
        max_tokens = chat_request.max_tokens if chat_request.max_tokens is not None else settings.MAX_TOKENS

        ai_result = await chat_service.generate_ai_response(
            conversation_id=chat_request.conversation_id,
            model_name=model_name,
            user_message=chat_request.message,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=chat_request.stream,
            api_key=current_user.api_key
        )

        usage = None
        if isinstance(ai_result, dict):
            ai_response = ai_result.get("content") or ""
            usage = ai_result.get("usage")
        else:
            ai_response = ai_result or ""

        if not ai_response:
            ai_response = "抱歉，我无法生成回复，请稍后重试。"

        # 计算费用信息
        cost_info = None
        if usage:
            try:
                pricing = await _get_model_pricing(model_name)
                if pricing.get("input") is not None and pricing.get("output") is not None:
                    prompt_tokens = int(usage.get("prompt_tokens") or usage.get("input_tokens") or 0)
                    completion_tokens = int(usage.get("completion_tokens") or usage.get("output_tokens") or 0)

                    input_price_per_token = float(pricing.get("input") or 0)
                    output_price_per_token = float(pricing.get("output") or 0)

                    prompt_cost = prompt_tokens * input_price_per_token
                    completion_cost = completion_tokens * output_price_per_token
                    total_cost = prompt_cost + completion_cost

                    cost_info = {
                        "prompt_tokens": prompt_tokens,
                        "completion_tokens": completion_tokens,
                        "total_tokens": prompt_tokens + completion_tokens,
                        "prompt_cost": prompt_cost,
                        "completion_cost": completion_cost,
                        "total_cost": total_cost
                    }
            except Exception as e:
                logger.warning(f"计算费用信息失败: {e}")

        # 创建新的AI消息
        ai_message = await chat_service.create_ai_message(
            api_key=current_user.api_key,
            conversation_id=chat_request.conversation_id,
            content=ai_response,
            model_name=model_name,
            is_error="抱歉" in ai_response or "错误" in ai_response,
            temperature=temperature,
            cost_info=cost_info
        )

        ai_message_id = ai_message.get('id') if ai_message else None

        logger.info(f"重新生成回复完成: AI消息={ai_message_id}")

        return {
            "success": True,
            "conversation_id": chat_request.conversation_id,
            "assistant_message_id": ai_message_id,
            "response": ai_response,
            "message": "重新生成回复完成"
        }

    except Exception as e:
        logger.error(f"重新生成回复异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新生成回复服务内部错误"
        )

@router.post('/upload-temp-image', response_model=TempImageUploadResponse)
async def upload_temp_image(req: TempImageUploadRequest, current_user = Depends(get_current_user_dependency)):
    """
    上传临时图片：仅保存base64到后端内存，返回图片ID供后续发送消息时引用。
    生产建议切换到Redis/数据库。
    """
    try:
        global TEMP_IMG_ID
        data = req.image_base64
        if not data:
            raise HTTPException(status_code=400, detail='缺少图片数据')
        # 提取size
        if data.startswith('data:'):
            _, _, b64 = data.partition(',')
            mime = req.mime_type or (data[5:data.find(';')] if ';' in data else None)
        else:
            b64 = data
            mime = req.mime_type
        try:
            size = len(b64decode(b64))
        except Exception:
            raise HTTPException(status_code=400, detail='图片base64解析失败')
        TEMP_IMG_ID += 1
        TEMP_IMAGES[TEMP_IMG_ID] = {
            'b64': b64,
            'mime': mime,
            'filename': req.filename,
            'size': size,
            'user_id': getattr(current_user, 'id', None)
        }
        return TempImageUploadResponse(image={
            'id': TEMP_IMG_ID,
            'filename': req.filename,
            'mime_type': mime,
            'size': size
        }, message='临时图片上传成功')
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'临时图片上传异常: {e}')
        raise HTTPException(status_code=500, detail='临时图片上传失败')

@router.post("/upload-image", response_model=ImageUploadResponse)
async def upload_image(
    req: ImageUploadRequest,
    current_user = Depends(get_current_user_dependency)
):
    """
    上传聊天图片，作为一条用户消息保存：
    - 将 base64 图片保存到 MinIO（生成 attachment URL）
    - 生成一条用户消息，content 为说明文字 + 图片链接（markdown）
    - 返回该用户消息信息
    """
    try:
        # 解析 base64
        data = req.image_base64
        if data.startswith('data:'):
            header, _, b64 = data.partition(',')
            mime = header[5:header.find(';')] if ';' in header else None
        else:
            b64 = data
            mime = req.mime_type
        image_bytes = b64decode(b64)

        # 先将图片保存到 MinIO
        temp_id = int(__import__('time').time() * 1000000)
        img_url = await minio_service.upload_image(temp_id, image_bytes, filename=req.filename, mime_type=mime or req.mime_type)
        if not img_url:
            raise HTTPException(status_code=500, detail="图片保存失败")

        # 组装一条带图片的用户消息（使用标准消息结构，content 写入 markdown 图片）
        caption = req.caption or "用户上传的图片"
        content_md = f"{caption}\n\n![]({img_url})"
        user_message = await chat_service.create_user_message(
            api_key=current_user.api_key,
            conversation_id=req.conversation_id,
            content=content_md,
            model_name=None
        )
        if not user_message:
            raise HTTPException(status_code=500, detail="创建用户消息失败")

        return ImageUploadResponse(
            conversation_id=req.conversation_id,
            user_message_id=user_message.get('id'),
            user_message=user_message,
            message="图片上传并创建消息成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传图片异常: {e}")
        raise HTTPException(status_code=500, detail="上传图片失败")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新生成回复服务内部错误"
        )

@router.delete("/messages/{message_id}", response_model=BaseResponse)
async def delete_message(
    message_id: int,
    current_user = Depends(get_current_user_dependency)
):
    """
    删除消息
    """
    try:
        logger.info(f"删除消息: {message_id}")

        # 使用新的 Record API 服务
        success = await record_api_service.delete_message(message_id)

        if success:
            logger.info(f"消息 {message_id} 删除成功")
            return BaseResponse(message="消息删除成功")
        else:
            logger.error(f"删除消息失败: message_id={message_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除消息失败"
            )

    except httpx.TimeoutException:
        logger.error("删除消息请求超时")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="消息服务请求超时"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除消息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除消息服务内部错误"
        )
