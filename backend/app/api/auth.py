# app/api/auth.py - 认证API路由

import logging
from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials

from ..models.schemas import UserLogin, LoginResponse, UserInfo, ErrorResponse
from ..services.auth_service import auth_service

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()

@router.post("/login", response_model=LoginResponse)
async def login(user_login: UserLogin):
    """
    用户登录
    
    使用API密钥进行登录，返回JWT访问令牌
    """
    try:
        # 验证API密钥
        user = await auth_service.verify_api_key(user_login.api_key)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥"
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户账户已被禁用"
            )
        
        # 创建访问令牌
        access_token = auth_service.create_access_token(user.id, user_login.api_key)
        
        logger.info(f"用户 {user.id} 登录成功")
        
        return LoginResponse(
            access_token=access_token,
            user=user,
            message="登录成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录服务内部错误"
        )

@router.post("/refresh")
async def refresh_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    刷新访问令牌
    """
    try:
        token = credentials.credentials
        new_token = auth_service.refresh_token(token)
        
        if not new_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌或令牌已过期"
            )
        
        return {
            "access_token": new_token,
            "token_type": "bearer",
            "message": "令牌刷新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新服务内部错误"
        )

@router.get("/me", response_model=UserInfo)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    获取当前用户信息
    """
    try:
        token = credentials.credentials
        user = await auth_service.get_current_user(token)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌或用户不存在"
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息服务内部错误"
        )

@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    用户登出
    
    注意：JWT是无状态的，实际的登出需要在客户端删除令牌
    这个接口主要用于日志记录和可能的令牌黑名单功能
    """
    try:
        token = credentials.credentials
        payload = auth_service.verify_token(token)
        
        if payload:
            user_id = payload.get("sub")
            logger.info(f"用户 {user_id} 登出")
        
        return {
            "success": True,
            "message": "登出成功"
        }
        
    except Exception as e:
        logger.error(f"登出异常: {e}")
        # 登出失败不应该阻止用户，返回成功
        return {
            "success": True,
            "message": "登出成功"
        }

# 依赖注入：获取当前用户
async def get_current_user_dependency(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserInfo:
    """
    依赖注入：获取当前认证用户
    
    用于需要认证的API端点
    """
    token = credentials.credentials
    user = await auth_service.get_current_user(token)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user
