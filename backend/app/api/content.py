# app/api/content.py - 内容代理API（用于代理获取 MinIO 预签名URL内容）

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import HTMLResponse, PlainTextResponse, Response, StreamingResponse
from ..services.minio_service import minio_service

router = APIRouter()

@router.get("/proxy")
async def proxy_content(url: str = Query(..., description="MinIO 预签名URL")):
    """
    代理获取 MinIO 预签名URL的内容，避免前端跨域问题。
    - HTML/JSON/TEXT 返回文本
    - 图片/二进制文件返回 StreamingResponse
    """
    try:
        print(f"🌐 代理请求URL: {url}")
        print(f"🔍 URL包含.json: {'.json' in url}")

        # 对于JSON文件，直接获取原始字节内容以保持JSON结构
        if ".json" in url:
            print(f"🔍 处理JSON文件: {url}")
            data, content_type = await minio_service.download_bytes_from_url(url)
            print(f"📥 下载结果: data={data is not None}, content_type={content_type}")
            if data is not None:
                try:
                    # 解码为文本并验证JSON格式
                    json_text = data.decode('utf-8')
                    print(f"📝 解码后文本长度: {len(json_text)}")
                    import json
                    json.loads(json_text)  # 验证JSON格式
                    print("✅ JSON格式验证成功")
                    return Response(json_text, media_type="application/json")
                except (UnicodeDecodeError, json.JSONDecodeError) as e:
                    print(f"❌ JSON处理失败: {e}")
                    # 如果解码或JSON解析失败，包装为标准格式
                    wrapped_content = {
                        "message_id": None,
                        "conversation_id": None,
                        "role": "unknown",
                        "content": data.decode('utf-8', errors='replace'),
                        "timestamp": None,
                        "format": "wrapped"
                    }
                    return Response(json.dumps(wrapped_content, ensure_ascii=False), media_type="application/json")
            else:
                print("❌ 字节下载失败，返回404")
                raise HTTPException(status_code=404, detail="JSON content not found")

        # 对于其他文件类型，使用文本方式下载
        content = await minio_service.download_content_from_url(url)
        if content is not None:
            if ".html" in url:
                return HTMLResponse(content)
            else:
                return PlainTextResponse(content)

        # 文本失败时，尝试二进制下载（适用于图片等静态资源）
        data, content_type = await minio_service.download_bytes_from_url(url)
        if data is None:
            raise HTTPException(status_code=404, detail="Content not found")
        return StreamingResponse(iter([data]), media_type=content_type)

    except HTTPException:
        raise
    except Exception:
        # 不暴露内部细节
        raise HTTPException(status_code=500, detail="Failed to fetch content")

