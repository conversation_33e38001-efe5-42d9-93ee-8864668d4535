# FastAPI 后端依赖包

# FastAPI 核心
fastapi==0.104.1
uvicorn[standard]==0.24.0

# HTTP 客户端
httpx==0.25.2
requests==2.31.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 数据验证
pydantic==2.5.0
pydantic-settings==2.1.0

# 对象存储
minio==7.2.0

# 环境变量
python-dotenv==1.0.0

# 日志
structlog==23.2.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# 其他工具
Jinja2==3.1.2
aiofiles==23.2.1
markdown==3.5.1
