# FastAPI 后端环境配置示例文件
# 复制此文件为 .env 并填入实际配置值

# 应用配置
APP_NAME=InspirFlow API
APP_VERSION=2.0.0
DEBUG=true
HOST=0.0.0.0
PORT=20010

# JWT 配置
SECRET_KEY=your-super-secret-key-here-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# API 服务配置
MODEL_API_BASE_URL=http://**************:20001
RECORD_API_BASE_URL=http://**************:20002

# MinIO 配置
MINIO_ENDPOINT=**************:7020
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=Rw80827mn@
MINIO_BUCKET=inspirflow-content
MINIO_SECURE=false

# 默认设置
DEFAULT_MODEL=gpt-3.5-turbo
DEFAULT_TEMPERATURE=0.8
MAX_TOKENS=4000

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
