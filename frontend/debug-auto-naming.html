<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试自动命名功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 调试自动命名功能</h1>
        
        <div class="test-section">
            <h3>测试对话标题更新API</h3>
            <input type="number" id="conversationId" placeholder="输入对话ID" value="88">
            <input type="text" id="newTitle" placeholder="输入新标题" value="测试自动命名功能">
            <button onclick="testUpdateTitle()">测试更新标题</button>
            <div id="titleResult" class="log-output"></div>
        </div>
        
        <div class="test-section">
            <h3>检查前端自动命名逻辑</h3>
            <button onclick="checkFrontendLogic()">检查前端逻辑</button>
            <div id="frontendResult" class="log-output"></div>
        </div>
        
        <div class="test-section">
            <h3>模拟第一条消息发送</h3>
            <input type="text" id="testMessage" placeholder="输入测试消息" value="请帮我写一个Python函数">
            <button onclick="simulateFirstMessage()">模拟发送第一条消息</button>
            <div id="simulateResult" class="log-output"></div>
        </div>
    </div>

    <script>
        // 获取token（假设已登录）
        const token = localStorage.getItem('auth_token');
        
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            element.scrollTop = element.scrollHeight;
        }
        
        async function testUpdateTitle() {
            const conversationId = document.getElementById('conversationId').value;
            const newTitle = document.getElementById('newTitle').value;
            const resultDiv = 'titleResult';
            
            document.getElementById(resultDiv).textContent = '';
            log(resultDiv, '开始测试更新对话标题...');
            
            if (!token) {
                log(resultDiv, '❌ 未找到认证token，请先登录');
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/conversations/${conversationId}/title`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ title: newTitle })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    log(resultDiv, '✅ 更新成功: ' + JSON.stringify(result, null, 2));
                } else {
                    log(resultDiv, '❌ 更新失败: ' + response.status + ' - ' + JSON.stringify(result, null, 2));
                }
            } catch (error) {
                log(resultDiv, '❌ 请求异常: ' + error.message);
            }
        }
        
        function checkFrontendLogic() {
            const resultDiv = 'frontendResult';
            document.getElementById(resultDiv).textContent = '';
            
            log(resultDiv, '检查前端自动命名逻辑...');
            
            // 模拟检查逻辑
            const messageText = "请帮我写一个Python函数";
            const title = messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText;
            
            log(resultDiv, '原始消息: "' + messageText + '"');
            log(resultDiv, '生成标题: "' + title + '"');
            log(resultDiv, '标题长度: ' + title.length);
            
            // 检查是否为第一条消息的逻辑
            const messages = []; // 模拟空消息列表
            const isFirstMessage = messages.length === 0;
            
            log(resultDiv, '是否为第一条消息: ' + isFirstMessage);
            log(resultDiv, '✅ 前端逻辑检查完成');
        }
        
        async function simulateFirstMessage() {
            const testMessage = document.getElementById('testMessage').value;
            const resultDiv = 'simulateResult';
            
            document.getElementById(resultDiv).textContent = '';
            log(resultDiv, '模拟发送第一条消息...');
            
            if (!token) {
                log(resultDiv, '❌ 未找到认证token，请先登录');
                return;
            }
            
            try {
                // 1. 创建新对话
                log(resultDiv, '步骤1: 创建新对话...');
                const createResponse = await fetch('/api/v1/conversations/', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ title: '新对话' })
                });
                
                if (!createResponse.ok) {
                    log(resultDiv, '❌ 创建对话失败: ' + createResponse.status);
                    return;
                }
                
                const createResult = await createResponse.json();
                const conversationId = createResult.conversation_id;
                log(resultDiv, '✅ 对话创建成功，ID: ' + conversationId);
                
                // 2. 发送消息
                log(resultDiv, '步骤2: 发送消息...');
                const chatResponse = await fetch('/api/v1/chat/', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        conversation_id: conversationId,
                        message: testMessage,
                        model_name: 'gpt-3.5-turbo',
                        temperature: 0.8
                    })
                });
                
                if (!chatResponse.ok) {
                    log(resultDiv, '❌ 发送消息失败: ' + chatResponse.status);
                    return;
                }
                
                log(resultDiv, '✅ 消息发送成功');
                
                // 3. 更新对话标题
                log(resultDiv, '步骤3: 更新对话标题...');
                const title = testMessage.length > 15 ? testMessage.substring(0, 15) + '...' : testMessage;
                
                const titleResponse = await fetch(`/api/v1/conversations/${conversationId}/title`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ title: title })
                });
                
                if (titleResponse.ok) {
                    log(resultDiv, '✅ 对话标题更新成功: "' + title + '"');
                } else {
                    log(resultDiv, '❌ 对话标题更新失败: ' + titleResponse.status);
                }
                
            } catch (error) {
                log(resultDiv, '❌ 模拟过程异常: ' + error.message);
            }
        }
        
        // 页面加载时检查token
        window.onload = function() {
            if (token) {
                console.log('✅ 找到认证token');
            } else {
                console.log('❌ 未找到认证token，请先登录');
            }
        };
    </script>
</body>
</html>
