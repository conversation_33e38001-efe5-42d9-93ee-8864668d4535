<template>
  <div class="chat-container">
    <!-- 侧边栏 -->
    <ChatSidebar
      :collapsed="sidebarCollapsed"
      :conversations="conversations"
      :current-conversation-id="currentConversationId"
      :models="models"
      v-model:selected-model="selectedModel"
      v-model:temperature="temperature"
      v-model:latex-render-enabled="latexRenderEnabled"
      :user-info="userInfo"
      @toggle-sidebar="toggleSidebar"
      @create-conversation="createNewConversation"
      @select-conversation="selectConversation"
      @delete-conversation="deleteConversation"
      @logout="logout"
    />

    <!-- 主聊天区域 -->
    <div class="main-content">
      <!-- 聊天消息区域 -->
      <ChatMessages
        ref="chatMessagesRef"
        :messages="messages"
        :is-generating="isGenerating"
        :message-contents="messageContents"
        :latex-render-enabled="latexRenderEnabled"
        @delete-message="deleteMessage"
      />

      <!-- 状态栏 -->
      <ChatStatusBar
        :is-generating="isGenerating"
        :selected-model-name="selectedModelName"
        :last-usage-tokens="lastUsageTokens"
        :last-cost-usd="lastCostUSD"
        :last-cost-breakdown="lastCostBreakdown"
      />

      <!-- 输入区域 -->
      <ChatInputArea
        v-model:input-message="inputMessage"
        :is-generating="isGenerating"
        :pending-previews="pendingPreviews"
        :uploading-image="uploadingImage"
        :upload-progress="uploadProgress"
        @send-message="sendMessage"
        @upload-change="onUploadChange"
        @open-image-viewer="openImageViewer"
        @remove-pending-attachment="removePendingAttachment"
      />
    </div>

    <!-- 图片预览查看器 -->
    <el-image-viewer
      v-if="imageViewerVisible"
      :url-list="imagePreviewList"
      :initial-index="imagePreviewIndex"
      @close="imageViewerVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { post } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'

// 导入组件
import ChatSidebar from '@/components/chat/ChatSidebar.vue'
import ChatMessages from '@/components/chat/ChatMessages.vue'
import ChatStatusBar from '@/components/chat/ChatStatusBar.vue'
import ChatInputArea from '@/components/chat/ChatInputArea.vue'

// 导入组合式API
import { useChat } from '@/composables/useChat.js'
import { useImageUpload } from '@/composables/useImageUpload.js'
import { useMessageProcessing } from '@/composables/useMessageProcessing.js'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 使用组合式API
const {
  conversations,
  models,
  messages,
  currentConversationId,
  selectedModel,
  temperature,
  inputMessage,
  isGenerating,
  messageContents,
  latexRenderEnabled,
  lastUsageTokens,
  lastCostUSD,
  lastCostBreakdown,
  selectedModelName,
  loadModels,
  loadConversations,
  selectConversation,
  createNewConversation,
  deleteConversation,
  deleteMessage,
  loadMessages
} = useChat()

const {
  uploadingImage,
  uploadProgress,
  pendingAttachments,
  pendingPreviews,
  imageViewerVisible,
  imagePreviewList,
  imagePreviewIndex,
  handleUploadChange,
  removePendingAttachment,
  openImageViewer,
  onMessageHtmlClick,
  clearPendingAttachments
} = useImageUpload()

const {
  processMessage,
  formatTime
} = useMessageProcessing()

// 响应式数据
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const userInfo = computed(() => authStore.userInfo)
const chatMessagesRef = ref()

// 图片上传处理
const onUploadChange = async (uploadFile) => {
  const result = await handleUploadChange(uploadFile)
  if (result) {
    inputMessage.value = (inputMessage.value || '') + result
  }
}



// 生命周期
onMounted(async () => {
  // 从localStorage加载LaTeX渲染设置
  const savedLatexSetting = localStorage.getItem('latexRenderEnabled')
  if (savedLatexSetting !== null) {
    latexRenderEnabled.value = savedLatexSetting === 'true'
  }



  await loadInitialData()
})

// 监听LaTeX渲染设置变化并保存到localStorage
watch(latexRenderEnabled, (newValue) => {
  localStorage.setItem('latexRenderEnabled', newValue.toString())
  console.log('💾 LaTeX渲染设置已保存:', newValue)
}, { immediate: false })



// 方法
const loadInitialData = async () => {
  try {
    appStore.setLoading(true, '加载数据中...')

    // 加载模型列表
    await loadModels()

    // 加载对话列表
    await loadConversations()

    // 如果有对话，选择第一个
    if (conversations.value.length > 0) {
      console.log('✅ 找到现有对话，选择第一个:', conversations.value[0])
      await selectConversation(conversations.value[0].id)
    } else {
      console.log('📝 没有现有对话，等待用户操作')
      // 确保当前对话ID为空
      currentConversationId.value = null
      messages.value = []
    }
  } catch (error) {
    console.error('加载初始数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    appStore.setLoading(false)
  }
}

// 侧边栏切换
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 登出
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要登出吗？', '确认登出', {
      type: 'warning'
    })

    authStore.logout()
    router.push('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('登出失败:', error)
    }
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isGenerating.value) return

  try {
    isGenerating.value = true
    const messageText = inputMessage.value.trim()

    // 确保有一个有效的对话
    let conversationId = currentConversationId.value
    
    // 如果没有选中对话，但有对话列表，优先选择第一个现有对话
    if (!conversationId && conversations.value.length > 0) {
      console.log('🔄 没有选中对话，选择第一个现有对话')
      await selectConversation(conversations.value[0].id)
      conversationId = currentConversationId.value
    }
    
    // 只有在完全没有对话的情况下才创建新对话
    if (!conversationId) {
      console.log('🆕 没有现有对话，创建新对话')
      // 创建新对话
      const createResponse = await post('/api/v1/conversations/', {
        title: messageText.length > 20 ? messageText.substring(0, 20) + '...' : messageText
      })
      
      if (createResponse.success && createResponse.conversation) {
        conversationId = createResponse.conversation.id
        conversations.value.unshift(createResponse.conversation)
        currentConversationId.value = conversationId
        messages.value = []
      } else {
        throw new Error('创建对话失败')
      }
    }

    // 检查是否为第一条消息（需要更新对话标题）
    const isFirstMessage = messages.value.length === 0

    const response = await post('/api/v1/chat/', {
      conversation_id: conversationId,
      message: messageText,
      model_name: selectedModel.value,
      temperature: temperature.value,
      attachments: pendingAttachments.value
    })

    if (response.success) {
      // 如果是第一条消息，自动更新对话标题
      if (isFirstMessage && currentConversationId.value) {
        try {
          // 生成对话标题（取前20个字符）
          const title = messageText.length > 20 ? messageText.substring(0, 20) + '...' : messageText

          const titleResponse = await post(`/api/v1/conversations/${currentConversationId.value}/title`, {
            title: title
          })

          if (titleResponse.success) {
            console.log('✅ 对话标题更新成功:', title)
            // 重新加载对话列表以更新标题显示
            await loadConversations()
          } else {
            console.warn('⚠️ 对话标题更新失败:', titleResponse.message)
          }
        } catch (titleError) {
          console.warn('⚠️ 更新对话标题时出错:', titleError)
        }
      }

      // 清空输入和附件
      inputMessage.value = ''
      clearPendingAttachments()

      // 更新消息列表
      await loadMessages(currentConversationId.value)

      // 更新计费信息
      if (response.usage) {
        lastUsageTokens.value = {
          prompt: response.usage.prompt_tokens,
          completion: response.usage.completion_tokens,
          total: response.usage.total_tokens
        }
      }

      if (response.cost) {
        lastCostUSD.value = response.cost.toFixed(6)
      }

      // 刷新用户余额（聊天完成后）
      try {
        await authStore.getCurrentUser()
      } catch (error) {
        console.warn('刷新用户余额失败:', error)
      }

      // 滚动到底部
      nextTick(() => {
        if (chatMessagesRef.value) {
          chatMessagesRef.value.scrollToBottom()
        }
      })
    } else {
      ElMessage.error(response.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    isGenerating.value = false
  }
}

// 获取安全的消息内容
const getSafeMessageContent = (message) => {
  if (!message) return ''

  // 优先使用缓存的内容
  if (messageContents.value.has(message.id)) {
    return messageContents.value.get(message.id)
  }

  // 如果有content_url，返回加载提示
  if (message.content_url) {
    return '<div class="loading-content">正在加载消息内容...</div>'
  }

  // 否则直接返回content
  return message.content || ''
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100vh;
  background: var(--bg-color-page);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.loading-content {
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-sm);
}
</style>