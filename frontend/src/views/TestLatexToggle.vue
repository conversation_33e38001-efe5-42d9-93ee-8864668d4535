<template>
  <div class="test-latex-toggle">
    <h1>LaTeX渲染切换功能测试</h1>
    
    <div class="controls">
      <div class="setting-row">
        <label>LaTeX渲染开关:</label>
        <el-switch
          v-model="latexRenderEnabled"
          size="default"
          active-text="开启"
          inactive-text="关闭"
        />
        <span class="status">当前状态: {{ latexRenderEnabled ? '开启' : '关闭' }}</span>
      </div>
      
      <div class="setting-row">
        <label>localStorage值:</label>
        <span class="storage-value">{{ localStorageValue }}</span>
        <el-button size="small" @click="refreshStorageValue">刷新</el-button>
      </div>
    </div>
    
    <div class="test-content">
      <h2>测试内容</h2>
      <div class="test-section">
        <h3>行内LaTeX公式</h3>
        <div class="rendered-content" v-html="processedInlineContent"></div>
      </div>
      
      <div class="test-section">
        <h3>块级LaTeX公式</h3>
        <div class="rendered-content" v-html="processedBlockContent"></div>
      </div>
      
      <div class="test-section">
        <h3>混合内容</h3>
        <div class="rendered-content" v-html="processedMixedContent"></div>
      </div>
    </div>
    
    <div class="instructions">
      <h3>测试说明</h3>
      <ol>
        <li>切换上方的LaTeX渲染开关</li>
        <li>观察下方的数学公式是否正确切换渲染状态</li>
        <li>刷新页面，检查设置是否被保存</li>
        <li>开启时应该显示渲染后的数学公式，关闭时应该显示原始的LaTeX代码</li>
      </ol>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useMessageProcessing } from '@/composables/useMessageProcessing.js'

const { processMessage } = useMessageProcessing()

// 响应式数据
const latexRenderEnabled = ref(true)
const localStorageValue = ref('')

// 测试内容
const inlineContent = '这是一个行内公式：$E = mc^2$，还有另一个：$\\sum_{i=1}^{n} x_i$'
const blockContent = '这是一个块级公式：\n$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$'
const mixedContent = `# 混合内容测试

这里有**粗体**和*斜体*文本。

行内公式：$\\alpha + \\beta = \\gamma$

代码块：
\`\`\`python
def calculate(x):
    return x ** 2
\`\`\`

块级公式：
$$\\frac{d}{dx}\\left( \\int_{0}^{x} f(u) \\, du\\right) = f(x)$$

更多文本内容。`

// 计算属性 - 处理后的内容
const processedInlineContent = computed(() => {
  return processMessage(inlineContent, {
    enableLatex: latexRenderEnabled.value,
    enableMarkdown: true
  })
})

const processedBlockContent = computed(() => {
  return processMessage(blockContent, {
    enableLatex: latexRenderEnabled.value,
    enableMarkdown: true
  })
})

const processedMixedContent = computed(() => {
  return processMessage(mixedContent, {
    enableLatex: latexRenderEnabled.value,
    enableMarkdown: true
  })
})

// 方法
const refreshStorageValue = () => {
  localStorageValue.value = localStorage.getItem('latexRenderEnabled') || '未设置'
}

// 生命周期
onMounted(() => {
  // 从localStorage加载设置
  const savedSetting = localStorage.getItem('latexRenderEnabled')
  if (savedSetting !== null) {
    latexRenderEnabled.value = savedSetting === 'true'
  }
  refreshStorageValue()
})

// 监听变化并保存到localStorage
watch(latexRenderEnabled, (newValue) => {
  localStorage.setItem('latexRenderEnabled', newValue.toString())
  console.log('💾 LaTeX渲染设置已保存:', newValue)
  refreshStorageValue()
}, { immediate: false })
</script>

<style scoped>
.test-latex-toggle {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.setting-row:last-child {
  margin-bottom: 0;
}

.setting-row label {
  font-weight: 600;
  min-width: 120px;
}

.status {
  font-weight: 500;
  color: #409eff;
}

.storage-value {
  font-family: monospace;
  background: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.test-content {
  margin-bottom: 30px;
}

.test-section {
  margin-bottom: 25px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.test-section h3 {
  background: #f8f9fa;
  margin: 0;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 16px;
}

.rendered-content {
  padding: 16px;
  background: white;
  min-height: 60px;
  line-height: 1.6;
}

.instructions {
  background: #e7f3ff;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.instructions h3 {
  margin-top: 0;
  color: #409eff;
}

.instructions ol {
  margin-bottom: 0;
}

.instructions li {
  margin-bottom: 8px;
}

/* 数学公式样式 */
:deep(.math-block) {
  text-align: center;
  margin: 16px 0;
}

:deep(.math-inline) {
  display: inline;
}

:deep(.math-error) {
  color: #f56c6c;
  background: #fef0f0;
  padding: 2px 4px;
  border-radius: 3px;
}
</style>
