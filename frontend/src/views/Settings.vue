<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1>设置</h1>
    </div>
    
    <div class="settings-content">
      <el-card class="setting-card">
        <template #header>
          <span>基本设置</span>
        </template>
        
        <el-form :model="settings" label-width="120px">
          <el-form-item label="默认模型">
            <el-select v-model="settings.defaultModel" placeholder="选择默认模型">
              <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
              <el-option label="GPT-4" value="gpt-4" />
              <el-option label="Claude-3" value="claude-3" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="默认温度">
            <el-slider
              v-model="settings.defaultTemperature"
              :min="0"
              :max="2"
              :step="0.1"
              show-input
            />
          </el-form-item>
          
          <el-form-item label="主题">
            <el-radio-group v-model="settings.theme">
              <el-radio label="light">浅色</el-radio>
              <el-radio label="dark">深色</el-radio>
              <el-radio label="auto">跟随系统</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="LaTeX渲染">
            <el-switch v-model="settings.enableLatex" />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveSettings">保存设置</el-button>
            <el-button @click="resetSettings">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const settings = reactive({
  defaultModel: 'gpt-3.5-turbo',
  defaultTemperature: 0.8,
  theme: 'light',
  enableLatex: true
})

onMounted(() => {
  loadSettings()
})

const loadSettings = () => {
  // 从localStorage加载设置
  const savedSettings = localStorage.getItem('userSettings')
  if (savedSettings) {
    Object.assign(settings, JSON.parse(savedSettings))
  }
}

const saveSettings = () => {
  // 保存设置到localStorage
  localStorage.setItem('userSettings', JSON.stringify(settings))
  ElMessage.success('设置已保存')
}

const resetSettings = () => {
  settings.defaultModel = 'gpt-3.5-turbo'
  settings.defaultTemperature = 0.8
  settings.theme = 'light'
  settings.enableLatex = true
}
</script>

<style scoped>
.settings-container {
  padding: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.settings-header {
  margin-bottom: var(--spacing-lg);
}

.settings-header h1 {
  color: var(--text-primary);
  font-size: var(--font-size-extra-large);
}

.setting-card {
  margin-bottom: var(--spacing-lg);
}
</style>
