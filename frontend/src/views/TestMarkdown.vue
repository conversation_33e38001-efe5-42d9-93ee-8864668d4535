<template>
  <div class="test-markdown">
    <h1>Markdown和LaTeX渲染测试</h1>
    
    <div class="test-section">
      <h2>原始内容</h2>
      <textarea v-model="testContent" rows="10" cols="80"></textarea>
      <button @click="processContent">处理内容</button>
    </div>
    
    <div class="test-section">
      <h2>处理后的内容</h2>
      <div class="rendered-content" v-html="processedContent"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useMessageProcessing } from '@/composables/useMessageProcessing.js'

const { processMessage } = useMessageProcessing()

const testContent = ref(`# 测试Markdown渲染

这是一个**粗体**文本和*斜体*文本。

## 代码块测试

\`\`\`python
def hello_world():
    print("Hello, World!")
    return 42
\`\`\`

## 行内代码测试

这是一个行内代码：\`console.log("Hello")\`

## 数学公式测试

这是一个行内公式：$E = mc^2$

这是一个块级公式：
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

## 列表测试

- 项目1
- 项目2
  - 子项目1
  - 子项目2

1. 有序列表1
2. 有序列表2
`)

const processedContent = ref('')

const processContent = () => {
  processedContent.value = processMessage(testContent.value, {
    enableLatex: true,
    enableMarkdown: true
  })
}

// 初始处理
processContent()
</script>

<style scoped>
.test-markdown {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 8px;
}

.rendered-content {
  border: 1px solid #eee;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
  min-height: 200px;
}

textarea {
  width: 100%;
  font-family: monospace;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  margin-top: 10px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}

/* 代码块样式 */
.rendered-content :deep(.code-block) {
  margin: 8px 0;
  border-radius: 6px;
  overflow: hidden;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
}

.rendered-content :deep(.code-block pre) {
  margin: 0;
  padding: 16px;
  background: transparent;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.rendered-content :deep(.inline-code) {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  border: 1px solid #e1e4e8;
}

/* 数学公式样式 */
.rendered-content :deep(.math-block) {
  margin: 12px 0;
  text-align: center;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e1e4e8;
}

.rendered-content :deep(.math-inline) {
  display: inline-block;
  margin: 0 2px;
}
</style>
