import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const loadingText = ref('加载中...')
  const sidebarCollapsed = ref(false)
  const networkStatus = ref(navigator.onLine)
  const theme = ref(localStorage.getItem('theme') || 'light')
  const language = ref(localStorage.getItem('language') || 'zh-CN')

  // 计算属性
  const isOnline = computed(() => networkStatus.value)
  const isDarkTheme = computed(() => theme.value === 'dark')

  // 动作
  const setLoading = (isLoading, text = '加载中...') => {
    loading.value = isLoading
    loadingText.value = text
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  const setNetworkStatus = (status) => {
    networkStatus.value = status
  }

  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新HTML根元素的class
    const html = document.documentElement
    if (newTheme === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  const toggleTheme = () => {
    const newTheme = theme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  const setLanguage = (newLanguage) => {
    language.value = newLanguage
    localStorage.setItem('language', newLanguage)
    
    // 更新HTML根元素的lang属性
    document.documentElement.lang = newLanguage
  }

  // 初始化应用设置
  const initializeApp = () => {
    // 恢复侧边栏状态
    const savedSidebarState = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarState !== null) {
      sidebarCollapsed.value = savedSidebarState === 'true'
    }
    
    // 应用主题
    setTheme(theme.value)
    
    // 应用语言
    setLanguage(language.value)
    
    // 监听网络状态变化
    window.addEventListener('online', () => setNetworkStatus(true))
    window.addEventListener('offline', () => setNetworkStatus(false))
  }

  return {
    // 状态
    loading: readonly(loading),
    loadingText: readonly(loadingText),
    sidebarCollapsed: readonly(sidebarCollapsed),
    networkStatus: readonly(networkStatus),
    theme: readonly(theme),
    language: readonly(language),
    
    // 计算属性
    isOnline,
    isDarkTheme,
    
    // 动作
    setLoading,
    toggleSidebar,
    setSidebarCollapsed,
    setNetworkStatus,
    setTheme,
    toggleTheme,
    setLanguage,
    initializeApp
  }
})
