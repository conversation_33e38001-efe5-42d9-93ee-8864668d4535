import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('access_token') || '')
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value)

  // 动作
  const login = async (apiKey) => {
    try {
      loading.value = true
      console.log('🔐 开始登录，API密钥:', apiKey.substring(0, 10) + '...')

      const response = await authAPI.login(apiKey)
      console.log('📡 登录API响应:', response)

      if (response.access_token && response.user) {
        // 保存token和用户信息
        token.value = response.access_token
        user.value = response.user

        // 持久化token
        localStorage.setItem('access_token', response.access_token)

        console.log('✅ 登录成功，用户:', response.user.id)
        ElMessage.success(response.message || '登录成功')
        return true
      } else {
        console.error('❌ 登录响应格式错误:', response)
        throw new Error('登录响应格式错误')
      }
    } catch (error) {
      console.error('❌ 登录失败详情:', error)

      let errorMessage = '登录失败'
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail
        console.error('服务器错误:', error.response.data)
      } else if (error.message) {
        errorMessage = error.message
      }

      // 检查是否是网络错误
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        errorMessage = '登录请求超时，请检查网络连接'
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        errorMessage = '网络连接失败，请检查网络设置'
      }

      console.error('显示错误消息:', errorMessage)
      ElMessage.error(errorMessage)
      return false
    } finally {
      loading.value = false
      console.log('🔄 登录流程结束，loading状态已重置')
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      
      // 调用登出API
      if (token.value) {
        try {
          await authAPI.logout()
        } catch (error) {
          console.warn('登出API调用失败:', error)
          // 即使API调用失败，也要清除本地状态
        }
      }
      
      // 清除本地状态
      clearAuthState()
      
      ElMessage.success('已安全登出')
    } catch (error) {
      console.error('登出失败:', error)
      // 即使出错也要清除本地状态
      clearAuthState()
    } finally {
      loading.value = false
    }
  }

  const getCurrentUser = async () => {
    try {
      if (!token.value) {
        throw new Error('未找到访问令牌')
      }
      
      const userData = await authAPI.getCurrentUser()
      user.value = userData
      return userData
    } catch (error) {
      console.error('获取用户信息失败:', error)
      
      // 如果是认证错误，清除本地状态
      if (error.response?.status === 401) {
        clearAuthState()
      }
      
      throw error
    }
  }

  const refreshToken = async () => {
    try {
      if (!token.value) {
        throw new Error('未找到访问令牌')
      }
      
      const response = await authAPI.refreshToken()
      
      if (response.access_token) {
        token.value = response.access_token
        localStorage.setItem('access_token', response.access_token)
        return true
      } else {
        throw new Error('刷新令牌响应格式错误')
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      
      // 刷新失败，清除认证状态
      clearAuthState()
      throw error
    }
  }

  const updateUserInfo = (newUserInfo) => {
    if (user.value) {
      user.value = { ...user.value, ...newUserInfo }
    }
  }

  const clearAuthState = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('access_token')
  }

  // 检查token是否即将过期（提前5分钟刷新）
  const checkTokenExpiry = () => {
    if (!token.value) return false
    
    try {
      // 解析JWT token
      const payload = JSON.parse(atob(token.value.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      const expiryTime = payload.exp
      
      // 如果token在5分钟内过期，返回true
      return (expiryTime - currentTime) < 300
    } catch (error) {
      console.error('解析token失败:', error)
      return true // 解析失败认为需要刷新
    }
  }

  // 自动刷新token
  const autoRefreshToken = async () => {
    if (checkTokenExpiry()) {
      try {
        await refreshToken()
        console.log('Token自动刷新成功')
      } catch (error) {
        console.error('Token自动刷新失败:', error)
      }
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    
    // 计算属性
    isAuthenticated,
    userInfo,
    
    // 动作
    login,
    logout,
    getCurrentUser,
    refreshToken,
    updateUserInfo,
    clearAuthState,
    checkTokenExpiry,
    autoRefreshToken
  }
})
