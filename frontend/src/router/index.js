import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由组件懒加载
const Login = () => import('@/views/Login.vue')
const Chat = () => import('@/views/Chat.vue')
const Settings = () => import('@/views/Settings.vue')
const Profile = () => import('@/views/Profile.vue')
const NotFound = () => import('@/views/NotFound.vue')

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/chat'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录 - InspirFlow',
      requiresAuth: false,
      hideForAuth: true // 已登录用户隐藏此页面
    }
  },
  {
    path: '/chat',
    name: 'Chat',
    component: Chat,
    meta: {
      title: '聊天 - InspirFlow',
      requiresAuth: true
    }
  },
  {
    path: '/chat/:conversationId',
    name: 'ChatConversation',
    component: Chat,
    meta: {
      title: '聊天 - InspirFlow',
      requiresAuth: true
    },
    props: true
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '设置 - InspirFlow',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人资料 - InspirFlow',
      requiresAuth: true
    }
  },
  {
    path: '/test-markdown',
    name: 'TestMarkdown',
    component: () => import('@/views/TestMarkdown.vue'),
    meta: {
      title: 'Markdown测试 - InspirFlow',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到 - InspirFlow',
      requiresAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  const authStore = useAuthStore()
  const isAuthenticated = authStore.isAuthenticated
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要认证但未登录，重定向到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 检查已登录用户是否应该隐藏某些页面
  if (to.meta.hideForAuth && isAuthenticated) {
    // 已登录用户访问登录页，重定向到聊天页
    next('/chat')
    return
  }
  
  // 验证路由参数
  if (to.name === 'ChatConversation') {
    const conversationId = parseInt(to.params.conversationId)
    if (isNaN(conversationId) || conversationId <= 0) {
      // 无效的对话ID，重定向到聊天首页
      next('/chat')
      return
    }
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 路由切换完成后的处理
  console.log(`路由切换: ${from.path} -> ${to.path}`)
  
  // 可以在这里添加页面访问统计等逻辑
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  
  // 可以在这里添加错误上报逻辑
})

export default router
