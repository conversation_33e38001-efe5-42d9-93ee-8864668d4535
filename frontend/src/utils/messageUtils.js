// codeUtils.js 已移除，不再需要导入

/**
 * 格式化时间显示
 * @param {string|Date} timestamp - 时间戳
 * @returns {string} - 格式化后的时间字符串
 */
export function formatTime(timestamp) {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 如果是今天
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
  
  // 如果是昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (date.getDate() === yesterday.getDate()) {
    return `昨天 ${date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })}`
  }
  
  // 其他日期
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}


/**
 * 生成消息ID
 * @returns {string} - 唯一的消息ID
 */
export function generateMessageId() {
  return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 创建消息对象
 * @param {Object} options - 消息选项
 * @param {string} options.content - 消息内容
 * @param {string} options.role - 消息角色 ('user' | 'assistant')
 * @param {string} [options.id] - 消息ID（可选，会自动生成）
 * @param {string} [options.timestamp] - 时间戳（可选，会自动生成）
 * @returns {Object} - 消息对象
 */
export function createMessage({ content, role, id, timestamp }) {
  return {
    id: id || generateMessageId(),
    content,
    role,
    timestamp: timestamp || new Date().toISOString(),
    created_at: timestamp || new Date().toISOString()
  }
}

/**
 * 验证消息对象
 * @param {Object} message - 消息对象
 * @returns {boolean} - 是否有效
 */
export function validateMessage(message) {
  if (!message || typeof message !== 'object') return false
  
  const requiredFields = ['id', 'content', 'role']
  return requiredFields.every(field => message.hasOwnProperty(field) && message[field])
}

/**
 * 过滤和清理消息内容
 * @param {string} content - 原始内容
 * @returns {string} - 清理后的内容
 */
export function sanitizeMessageContent(content) {
  if (!content) return ''
  
  // 移除潜在的危险HTML标签
  const dangerousTags = /<script[^>]*>.*?<\/script>/gi
  content = content.replace(dangerousTags, '')
  
  // 移除事件处理器（除了我们自己的复制按钮）
  content = content.replace(/on\w+\s*=\s*"[^"]*"/gi, (match) => {
    if (match.includes('copyCodeToClipboard')) {
      return match
    }
    return ''
  })
  
  return content.trim()
}

/**
 * 计算消息字符数
 * @param {string} content - 消息内容
 * @returns {number} - 字符数
 */
export function getMessageLength(content) {
  if (!content) return 0
  
  // 移除HTML标签后计算长度
  const textContent = content.replace(/<[^>]*>/g, '')
  return textContent.length
}

/**
 * 截取消息预览
 * @param {string} content - 消息内容
 * @param {number} maxLength - 最大长度
 * @returns {string} - 预览内容
 */
export function getMessagePreview(content, maxLength = 100) {
  if (!content) return ''
  
  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, '')
  
  if (textContent.length <= maxLength) {
    return textContent
  }
  
  return textContent.substring(0, maxLength) + '...'
}
