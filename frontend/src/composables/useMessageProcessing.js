import { ref } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import katex from 'katex'

export function useMessageProcessing() {
  // 处理消息内容，为代码块添加语法高亮
  const processMessageContentWithCodeBlocks = (htmlContent) => {
    if (!htmlContent) return htmlContent

    // 使用字符串替换的方式处理代码块
    let processedContent = htmlContent

    // 为代码块添加语法高亮
    processedContent = processedContent.replace(
      /<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g, 
      (_, attributes, codeContent) => {
        // 提取语言信息
        const languageMatch = attributes.match(/class="language-([a-zA-Z0-9_+\-#]+)"/) || 
                             attributes.match(/class="([a-zA-Z0-9_+\-#]+)"/)
        const language = languageMatch ? languageMatch[1] : ''

        // 提取纯文本内容
        let plainText = ''

        try {
          // 处理HTML实体和标签，保持换行符
          plainText = codeContent
            .replace(/<br\s*\/?>/gi, '\n')  // 将<br>转换为换行符
            .replace(/<[^>]*>/g, '')        // 移除所有HTML标签
            .replace(/&lt;/g, '<')          // 解码HTML实体
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&nbsp;/g, ' ')

          // 如果没有换行符，尝试DOM解析
          if (!plainText.includes('\n') && codeContent.includes('\n')) {
            try {
              const tempDiv = document.createElement('div')
              tempDiv.innerHTML = codeContent.replace(/<br\s*\/?>/gi, '\n')
              plainText = tempDiv.textContent || tempDiv.innerText || ''
            } catch (domError) {
              console.warn('DOM解析失败:', domError)
            }
          }

          // 确保有内容
          if (!plainText || plainText.trim() === '') {
            plainText = codeContent.replace(/<[^>]*>/g, '') || codeContent
          }

          // 清理多余的空白，但保持换行符
          plainText = plainText.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

        } catch (error) {
          console.warn('代码内容提取失败，使用原始内容:', error)
          plainText = codeContent.replace(/<[^>]*>/g, '') || codeContent
        }

        // 应用语法高亮
        let highlightedCode = ''
        try {
          if (language && hljs.getLanguage(language)) {
            // 如果指定了语言且支持，使用指定语言高亮
            highlightedCode = hljs.highlight(plainText, { language }).value
          } else {
            // 否则尝试自动检测语言
            const result = hljs.highlightAuto(plainText)
            highlightedCode = result.value
          }
        } catch (e) {
          console.warn('代码高亮失败:', e)
          highlightedCode = plainText // 降级到纯文本
        }

        // 为复制功能编码代码内容
        const encodedCode = encodeURIComponent(plainText)

        return `
          <div class="enhanced-code-block">
            <div class="code-header">
              ${language ? `<span class="code-language">${language.toUpperCase()}</span>` : ''}
              <button class="code-copy-btn" data-code="${encodedCode}" title="复制代码">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
                <span class="copy-text">复制</span>
              </button>
            </div>
            <div class="code-content">
              <pre><code class="hljs ${language ? `language-${language}` : ''}">${highlightedCode}</code></pre>
            </div>
          </div>
        `
      }
    )

    return processedContent
  }

  // 处理LaTeX渲染
  const processLatexContent = (content, enabled = true) => {
    if (!enabled || !content) return content

    try {
      // 处理块级LaTeX: $$...$$（必须先处理，避免与行内公式冲突）
      content = content.replace(/\$\$([\s\S]+?)\$\$/g, (match, latex) => {
        try {
          const rendered = katex.renderToString(latex.trim(), {
            displayMode: true,
            throwOnError: false
          })
          return `<div class="math-block">${rendered}</div>`
        } catch (e) {
          console.warn('块级LaTeX渲染失败:', e)
          return `<div class="math-block math-error">$$${latex}$$</div>`
        }
      })

      // 处理行内LaTeX: $...$
      content = content.replace(/\$([^$\n]+)\$/g, (match, latex) => {
        try {
          const rendered = katex.renderToString(latex.trim(), {
            displayMode: false,
            throwOnError: false
          })
          return `<span class="math-inline">${rendered}</span>`
        } catch (e) {
          console.warn('行内LaTeX渲染失败:', e)
          return `<span class="math-inline math-error">$${latex}$</span>`
        }
      })
    } catch (error) {
      console.warn('LaTeX处理失败:', error)
    }

    return content
  }

  // 处理Markdown内容（保护LaTeX公式）
  const processMarkdownContent = (content) => {
    if (!content) return ''

    try {
      // 先保护LaTeX公式，避免被Markdown处理
      const latexPlaceholders = []
      let protectedContent = content

      // 保护块级LaTeX: $$...$$
      protectedContent = protectedContent.replace(/\$\$([\s\S]+?)\$\$/g, (_, latex) => {
        const index = latexPlaceholders.length
        const placeholder = `LATEXBLOCKPLACEHOLDER${index}LATEXBLOCKPLACEHOLDER`
        latexPlaceholders.push({ type: 'block', content: latex.trim(), placeholder })
        return placeholder
      })

      // 保护行内LaTeX: $...$
      protectedContent = protectedContent.replace(/\$([^$\n]+)\$/g, (_, latex) => {
        const index = latexPlaceholders.length
        const placeholder = `LATEXINLINEPLACEHOLDER${index}LATEXINLINEPLACEHOLDER`
        latexPlaceholders.push({ type: 'inline', content: latex.trim(), placeholder })
        return placeholder
      })

      // 配置marked选项
      marked.setOptions({
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value
            } catch (err) {
              console.warn('代码高亮失败:', err)
            }
          }
          return hljs.highlightAuto(code).value
        },
        breaks: true,
        gfm: true
      })

      // 处理Markdown
      let processed = marked(protectedContent)

      // 为图片添加样式类和点击处理
      processed = processed.replace(
        /<img([^>]*?)src="([^"]*)"([^>]*?)>/g,
        '<img$1src="$2"$3 class="message-image" style="max-width: 100%; max-height: 400px; cursor: pointer; border-radius: 8px; margin: 8px 0;">'
      )

      // 将标准的 <pre><code> 转换为带复制按钮和语言标识的增强结构
      processed = processMessageContentWithCodeBlocks(processed)

      // 恢复LaTeX公式并渲染
      latexPlaceholders.forEach((item, index) => {
        try {
          const rendered = katex.renderToString(item.content, {
            displayMode: item.type === 'block',
            throwOnError: false
          })

          const wrapper = item.type === 'block'
            ? `<div class="math-block">${rendered}</div>`
            : `<span class="math-inline">${rendered}</span>`

          // 使用全局替换确保所有占位符都被替换
          processed = processed.replaceAll(item.placeholder, wrapper)
        } catch (e) {
          console.warn('LaTeX渲染失败:', e, 'content:', item.content)
          const fallback = item.type === 'block'
            ? `<div class="math-block math-error">$$${item.content}$$</div>`
            : `<span class="math-inline math-error">$${item.content}$</span>`
          processed = processed.replaceAll(item.placeholder, fallback)
        }
      })

      return processed
    } catch (error) {
      console.warn('Markdown处理失败:', error)
      return content
    }
  }

  // 综合处理消息内容
  const processMessage = (content, options = {}) => {
    if (!content) return ''

    const {
      enableLatex = true,
      enableMarkdown = true
    } = options

    // 如果启用Markdown，使用集成的Markdown处理（包含LaTeX和代码高亮）
    if (enableMarkdown && typeof content === 'string') {
      return processMarkdownContent(content)
    }

    // 否则只处理LaTeX
    if (enableLatex) {
      return processLatexContent(content, enableLatex)
    }

    return content
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return ''
    
    try {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date

      // 如果是今天
      if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
        return date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      }
      
      // 如果是昨天
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      if (date.getDate() === yesterday.getDate()) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      }
      
      // 其他情况显示完整日期
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      console.warn('时间格式化失败:', error)
      return timestamp
    }
  }

  return {
    processMessageContentWithCodeBlocks,
    processLatexContent,
    processMarkdownContent,
    processMessage,
    formatTime
  }
}
