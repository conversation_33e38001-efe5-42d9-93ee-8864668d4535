import { ref } from 'vue'
import { post } from '@/api'
import { ElMessage } from 'element-plus'

export function useImageUpload() {
  // 响应式数据
  const uploadingImage = ref(false)
  const uploadProgress = ref(0)
  const pendingAttachments = ref([]) // 临时图片ID列表
  const pendingPreviews = ref([]) // 本地预览数据列表
  const imageViewerVisible = ref(false)
  const imagePreviewList = ref([])
  const imagePreviewIndex = ref(0)

  // 文件转base64
  const fileToBase64 = (file) => new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })

  // 处理图片上传
  const handleUploadChange = async (uploadFile) => {
    const file = uploadFile.raw || uploadFile

    console.log('🖼️ 开始处理图片上传:', file.name, file.size, file.type)
    try {
      // 本地预览
      console.log('📄 转换文件为base64...')
      const base64 = await fileToBase64(file)
      console.log('✅ base64转换完成，长度:', base64.length)

      // 进度条
      uploadingImage.value = true
      uploadProgress.value = 0
      console.log('📊 开始上传，设置进度状态')

      // 调用临时上传接口
      const payload = { 
        image_base64: base64, 
        filename: file.name, 
        mime_type: file.type 
      }
      console.log('🚀 发送上传请求到 /api/v1/chat/upload-temp-image')
      
      const resp = await post('/api/v1/chat/upload-temp-image', payload, {
        onUploadProgress: (e) => {
          if (e.total) {
            uploadProgress.value = Math.round((e.loaded * 100) / e.total)
            console.log('📈 上传进度:', uploadProgress.value + '%')
          }
        }
      })
      console.log('📥 上传响应:', resp)

      if (resp && resp.success && resp.image?.id) {
        // 记录临时图片ID
        pendingAttachments.value.push(resp.image.id)
        console.log('📎 添加到待发送附件:', resp.image.id, '当前附件列表:', pendingAttachments.value)

        // 本地预览列表
        pendingPreviews.value.push({ 
          id: resp.image.id, 
          url: base64, 
          name: file.name 
        })
        console.log('🖼️ 添加到预览列表，当前预览数量:', pendingPreviews.value.length)

        ElMessage.success('图片上传成功，可继续编辑消息后发送')
        console.log('✅ 图片上传处理完成')
        
        return `\n[已添加图片: ${file.name}]\n`
      } else {
        console.error('❌ 上传失败，响应:', resp)
        ElMessage.error(resp?.message || '图片上传失败')
        return ''
      }
    } catch (e) {
      console.error('❌ 上传图片异常:', e)
      ElMessage.error('图片上传失败')
      return ''
    } finally {
      uploadingImage.value = false
      console.log('🏁 上传处理结束，重置状态')
    }
  }

  // 移除待发送的附件
  const removePendingAttachment = (id) => {
    pendingPreviews.value = pendingPreviews.value.filter(p => p.id !== id)
    pendingAttachments.value = pendingAttachments.value.filter(x => x !== id)
  }

  // 打开图片查看器
  const openImageViewer = (url) => {
    imagePreviewList.value = [url]
    imagePreviewIndex.value = 0
    imageViewerVisible.value = true
  }

  // 处理消息HTML中的图片点击
  const onMessageHtmlClick = (e) => {
    const img = e.target.closest('img')
    if (!img) return
    const src = img.getAttribute('src')
    if (!src) return
    const proxied = /^\/api\/v1\/content\/proxy\?/.test(src) 
      ? src 
      : `/api/v1/content/proxy?url=${encodeURIComponent(src)}`
    openImageViewer(proxied)
  }

  // 清空待发送的附件
  const clearPendingAttachments = () => {
    pendingAttachments.value = []
    pendingPreviews.value = []
  }

  return {
    // 响应式数据
    uploadingImage,
    uploadProgress,
    pendingAttachments,
    pendingPreviews,
    imageViewerVisible,
    imagePreviewList,
    imagePreviewIndex,

    // 方法
    handleUploadChange,
    removePendingAttachment,
    openImageViewer,
    onMessageHtmlClick,
    clearPendingAttachments
  }
}
