import { ref, computed, nextTick } from 'vue'
import { get, post, del } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useMessageProcessing } from './useMessageProcessing.js'

export function useChat() {
  // 响应式数据
  const conversations = ref([])
  const models = ref([])
  const messages = ref([])
  const currentConversationId = ref(null)
  const selectedModel = ref(null)
  const temperature = ref(0.8)
  const inputMessage = ref('')
  const isGenerating = ref(false)
  const messageContents = ref(new Map())
  const latexRenderEnabled = ref(true)

  // 计费显示
  const lastUsageTokens = ref(null)
  const lastCostUSD = ref('')
  const lastCostBreakdown = ref('')

  // 计算属性
  const selectedModelName = computed(() => {
    const model = models.value.find(m => m.id === selectedModel.value)
    return model?.name || selectedModel.value
  })

  // 加载模型列表
  const loadModels = async () => {
    try {
      console.log('🔄 正在加载模型列表...')
      const response = await get('/api/v1/models/')
      console.log('📋 模型API响应:', response)

      if (response.success && response.models) {
        models.value = response.models.map(model => ({
          id: model.id,
          name: model.display_name || model.name || model.id,
          description: model.description,
          in_cost: model.input_cost_per_token || (model.pricing && model.pricing.input_token) || 0,
          out_cost: model.output_cost_per_token || (model.pricing && model.pricing.output_token) || 0,
        }))
        console.log('✅ 模型加载成功:', models.value)

        // 设置默认选中第一个模型
        if (models.value.length > 0) {
          selectedModel.value = models.value[0].id
          console.log('🎯 设置默认模型:', models.value[0].name)
        }
      } else {
        console.error('❌ 模型API返回格式错误:', response)
        ElMessage.error('获取模型列表失败')
      }
    } catch (error) {
      console.error('❌ 加载模型失败:', error)
      ElMessage.error('连接模型服务失败，请检查网络连接')
    }
  }

  // 加载对话列表
  const loadConversations = async () => {
    try {
      console.log('🔄 正在加载对话列表...')
      const response = await get('/api/v1/conversations/')
      console.log('💬 对话API响应:', response)

      if (response.success && response.conversations) {
        conversations.value = response.conversations
        console.log('✅ 对话加载成功:', conversations.value)
      } else {
        console.log('📝 暂无对话历史')
        conversations.value = []
      }
    } catch (error) {
      console.error('❌ 加载对话失败:', error)
      conversations.value = []
    }
  }

  // 选择对话
  const selectConversation = async (conversationId) => {
    try {
      console.log('🔄 选择对话:', conversationId)
      currentConversationId.value = conversationId

      // 调用后端API设置当前对话
      try {
        const resp = await fetch(`/api/v1/conversations/${conversationId}/set-current`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`,
            'Content-Type': 'application/json'
          }
        })
        if (resp.ok) {
          const data = await resp.json().catch(() => null)
          if (data?.success) {
            console.log('✅ 当前对话设置成功:', data.message)
          }
        }
      } catch (error) {
        console.warn('⚠️ 设置当前对话失败:', error)
      }

      // 加载对话消息
      await loadMessages(conversationId)
    } catch (error) {
      console.error('❌ 选择对话失败:', error)
      ElMessage.error('加载对话失败')
    }
  }

  // 加载消息列表
  const loadMessages = async (conversationId) => {
    try {
      console.log('🔄 正在加载消息列表...')
      const response = await get(`/api/v1/conversations/${conversationId}/messages`)
      console.log('📨 消息API响应:', response)

      if (response.success && response.messages) {
        messages.value = response.messages
        console.log('✅ 消息加载成功:', messages.value.length, '条消息')

        // 加载每条消息的内容
        for (const message of messages.value) {
          if (message.content_url) {
            await loadMessageContent(message)
          }
        }
      } else {
        console.log('📝 暂无消息历史')
        messages.value = []
      }
    } catch (error) {
      console.error('❌ 加载消息失败:', error)
      messages.value = []
    }
  }

  // 加载消息内容
  const loadMessageContent = async (message) => {
    if (!message.content_url) return

    try {
      const response = await fetch(message.content_url)
      if (response.ok) {
        const data = await response.json()
        if (data.content) {
          // 处理消息内容
          const processedContent = processMessageContent(data.content)
          messageContents.value.set(message.id, processedContent)
        }
      }
    } catch (error) {
      console.warn('加载消息内容失败:', error)
      messageContents.value.set(message.id, message.content || '消息加载失败')
    }
  }

  // 使用统一的消息处理逻辑
  const { processMessage } = useMessageProcessing()

  // 处理消息内容
  const processMessageContent = (content) => {
    if (!content) return ''

    return processMessage(content, {
      enableLatex: latexRenderEnabled.value,
      enableMarkdown: true
    })
  }

  // 创建新对话
  const createNewConversation = async () => {
    try {
      const response = await post('/api/v1/conversations/', {
        title: '新对话'
      })

      if (response.success && response.conversation) {
        conversations.value.unshift(response.conversation)
        await selectConversation(response.conversation.id)
        ElMessage.success('新对话创建成功')
      } else {
        ElMessage.error('创建对话失败')
      }
    } catch (error) {
      console.error('创建对话失败:', error)
      ElMessage.error('创建对话失败')
    }
  }

  // 删除对话
  const deleteConversation = async (conversationId) => {
    try {
      await ElMessageBox.confirm('确定要删除这个对话吗？', '确认删除', {
        type: 'warning'
      })

      // 使用正确的DELETE方法和路径
      const response = await del(`/api/v1/conversations/${conversationId}`)

      if (response.success) {
        conversations.value = conversations.value.filter(c => c.id !== conversationId)

        if (currentConversationId.value === conversationId) {
          if (conversations.value.length > 0) {
            await selectConversation(conversations.value[0].id)
          } else {
            currentConversationId.value = null
            messages.value = []
          }
        }

        ElMessage.success('对话删除成功')
      } else {
        ElMessage.error('删除对话失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除对话失败:', error)
        ElMessage.error('删除对话失败')
      }
    }
  }

  // 删除消息
  const deleteMessage = async (messageId) => {
    try {
      await ElMessageBox.confirm('确定要删除这条消息吗？', '确认删除', {
        type: 'warning'
      })

      // 使用正确的DELETE方法和路径
      const response = await del(`/api/v1/chat/messages/${messageId}`)

      if (response.success) {
        messages.value = messages.value.filter(m => m.id !== messageId)
        messageContents.value.delete(messageId)
        ElMessage.success('消息删除成功')
      } else {
        ElMessage.error('删除消息失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除消息失败:', error)
        ElMessage.error('删除消息失败')
      }
    }
  }

  return {
    // 响应式数据
    conversations,
    models,
    messages,
    currentConversationId,
    selectedModel,
    temperature,
    inputMessage,
    isGenerating,
    messageContents,
    latexRenderEnabled,
    lastUsageTokens,
    lastCostUSD,
    lastCostBreakdown,
    
    // 计算属性
    selectedModelName,
    
    // 方法
    loadModels,
    loadConversations,
    selectConversation,
    loadMessages,
    loadMessageContent,
    createNewConversation,
    deleteConversation,
    deleteMessage
  }
}
