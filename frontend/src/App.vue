<template>
  <div
    id="app"
    class="app-container"
    v-loading="globalLoading"
    :element-loading-text="loadingText"
    element-loading-background="rgba(0, 0, 0, 0.7)"
    element-loading-spinner="el-icon-loading"
  >
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition name="fade" mode="out-in">
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>

    <!-- 全局消息提示容器 -->
    <div id="message-container"></div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 计算属性
const globalLoading = computed(() => appStore.loading)
const loadingText = computed(() => appStore.loadingText)

// 生命周期
onMounted(async () => {
  // 初始化应用
  await initializeApp()
  
  // 监听网络状态
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// 方法
const initializeApp = async () => {
  try {
    appStore.setLoading(true, '正在初始化应用...')
    
    // 检查用户登录状态
    const token = localStorage.getItem('access_token')
    if (token) {
      try {
        await authStore.getCurrentUser()
        
        // 如果当前在登录页面，重定向到主页
        if (router.currentRoute.value.path === '/login') {
          router.push('/chat')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 清除无效token
        localStorage.removeItem('access_token')
        if (router.currentRoute.value.path !== '/login') {
          router.push('/login')
        }
      }
    } else {
      // 未登录，重定向到登录页
      if (router.currentRoute.value.path !== '/login') {
        router.push('/login')
      }
    }
  } catch (error) {
    console.error('应用初始化失败:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    appStore.setLoading(false)
  }
}

const handleOnline = () => {
  appStore.setNetworkStatus(true)
  ElMessage.success('网络连接已恢复')
}

const handleOffline = () => {
  appStore.setNetworkStatus(false)
  ElMessage.warning('网络连接已断开')
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面隐藏时的处理
    console.log('页面已隐藏')
  } else {
    // 页面显示时的处理
    console.log('页面已显示')
    
    // 如果用户已登录，刷新用户信息
    if (authStore.isAuthenticated) {
      authStore.getCurrentUser().catch(console.error)
    }
  }
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<style>
/* 全局样式 */
#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Element Plus 样式覆盖 */
.el-message {
  min-width: 300px;
  font-weight: 500;
}

.el-loading-mask {
  backdrop-filter: blur(4px);
}

.el-loading-text {
  font-weight: 500;
  font-size: 16px;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
