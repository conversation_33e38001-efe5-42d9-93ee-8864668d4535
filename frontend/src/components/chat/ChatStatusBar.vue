<template>
  <div class="status-bar">
    <div class="status-info">
      <span>状态: {{ isGenerating ? '生成中' : '就绪' }}</span>
      <span>模型: {{ selectedModelName }}</span>
      <span v-if="lastUsageTokens">
        本次: {{ lastUsageTokens.prompt }}/{{ lastUsageTokens.completion }} tokens
      </span>
      <span v-if="lastCostUsd">费用: ${{ lastCostUsd }}</span>
      <span v-if="lastCostBreakdown" style="opacity:0.85;">{{ lastCostBreakdown }}</span>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isGenerating: {
    type: Boolean,
    default: false
  },
  selectedModelName: {
    type: String,
    default: ''
  },
  lastUsageTokens: {
    type: Object,
    default: null
  },
  lastCostUsd: {
    type: String,
    default: ''
  },
  lastCostBreakdown: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.status-bar {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-color-light);
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.status-info {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  flex-wrap: wrap;
}

.status-info span {
  white-space: nowrap;
}

@media (max-width: 768px) {
  .status-info {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
