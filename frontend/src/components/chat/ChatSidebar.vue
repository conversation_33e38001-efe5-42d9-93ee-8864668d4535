<template>
  <div class="sidebar" :class="{ collapsed: collapsed }">
    <div class="sidebar-header">
      <h2 v-if="!collapsed">InspirFlow</h2>
      <el-button
        :icon="collapsed ? Expand : Fold"
        circle
        size="small"
        @click="$emit('toggle-sidebar')"
      />
    </div>

    <!-- 对话列表 -->
    <div class="conversations-section">
      <div class="section-title" v-if="!collapsed">
        <span>对话列表</span>
        <el-button :icon="Plus" size="small" @click="$emit('create-conversation')">
          新建对话
        </el-button>
      </div>
      <div class="conversations-list">
        <div
          v-for="conv in conversations"
          :key="conv.id"
          class="conversation-item"
          :class="{ active: currentConversationId === conv.id }"
        >
          <div class="conversation-content" @click="$emit('select-conversation', conv.id)">
            <el-icon><ChatDotRound /></el-icon>
            <span v-if="!collapsed" class="conversation-title">
              {{ conv.title || '新对话' }}
            </span>
          </div>
          <el-button
            v-if="!collapsed"
            :icon="Delete"
            size="small"
            type="danger"
            text
            class="delete-btn"
            @click.stop="$emit('delete-conversation', conv.id)"
          />
        </div>
      </div>
    </div>

    <!-- 模型设置 -->
    <div class="model-section" v-if="!collapsed">
      <div class="section-title">模型设置</div>
      <el-select 
        :model-value="selectedModel" 
        @update:model-value="$emit('update:selectedModel', $event)"
        placeholder="选择模型" 
        size="small"
      >
        <el-option
          v-for="model in models"
          :key="model.id"
          :label="model.name"
          :value="model.id"
        />
      </el-select>

      <div class="temperature-setting">
        <label>温度: {{ temperature }}</label>
        <el-slider
          :model-value="temperature"
          @update:model-value="$emit('update:temperature', $event)"
          :min="0"
          :max="2"
          :step="0.1"
          size="small"
        />
      </div>

      <!-- LaTeX渲染设置 -->
      <div class="latex-setting">
        <div class="setting-row">
          <label>LaTeX渲染</label>
          <el-switch
            :model-value="latexRenderEnabled"
            @update:model-value="$emit('update:latexRenderEnabled', $event)"
            size="small"
          />
        </div>
      </div>
    </div>

    <!-- 用户信息 -->
    <div class="user-section" v-if="!collapsed">
      <div class="user-info">
        <div class="user-name">{{ userInfo?.username || '用户' }}</div>
        <div class="user-balance">余额: ${{ userInfo?.current_balance || 0 }}</div>
      </div>
      <el-button size="small" @click="$emit('logout')">登出</el-button>
    </div>
  </div>
</template>

<script setup>
import { ChatDotRound, Plus, Expand, Fold, Delete } from '@element-plus/icons-vue'

defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  conversations: {
    type: Array,
    default: () => []
  },
  currentConversationId: {
    type: Number,
    default: null
  },
  models: {
    type: Array,
    default: () => []
  },
  selectedModel: {
    type: String,
    default: null
  },
  temperature: {
    type: Number,
    default: 0.8
  },
  latexRenderEnabled: {
    type: Boolean,
    default: true
  },
  userInfo: {
    type: Object,
    default: null
  }
})

defineEmits([
  'toggle-sidebar',
  'create-conversation',
  'select-conversation',
  'delete-conversation',
  'update:selectedModel',
  'update:temperature',
  'update:latexRenderEnabled',
  'logout'
])
</script>

<style scoped>
.sidebar {
  width: 300px;
  background: var(--bg-color);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  transition: var(--transition-base);
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h2 {
  font-size: var(--font-size-large);
  color: var(--text-primary);
  margin: 0;
}

.conversations-section {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-small);
}

.conversations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: var(--transition-base);
}

.conversation-item:hover {
  background: var(--bg-color-light);
}

.conversation-item.active {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

.conversation-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;
}

.conversation-title {
  margin-left: var(--spacing-sm);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-section, .user-section {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.temperature-setting {
  margin-top: var(--spacing-sm);
}

.temperature-setting label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-small);
  color: var(--text-regular);
}

.latex-setting {
  margin-top: var(--spacing-sm);
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  margin-bottom: var(--spacing-sm);
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
}

.user-balance {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}
</style>
