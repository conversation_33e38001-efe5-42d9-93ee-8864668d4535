<template>
  <div class="chat-messages" ref="messagesContainer">
    <div
      v-for="message in messages"
      :key="message.id"
      class="message-wrapper"
      :class="message.role"
    >
      <MessageItem
        :message="message"
        :message-contents="messageContents"
        :latex-render-enabled="latexRenderEnabled"
        :show-actions="true"
        @delete-message="$emit('delete-message', $event)"
        @open-image-viewer="openImageViewer"
      />
    </div>

    <!-- 加载指示器 -->
    <div v-if="isGenerating" class="generating-indicator">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>AI正在思考中...</span>
    </div>

    <!-- 图片预览查看器 -->
    <el-image-viewer
      v-if="imageViewerVisible"
      :url-list="imagePreviewList"
      :initial-index="imagePreviewIndex"
      @close="imageViewerVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import MessageItem from './MessageItem.vue'

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  },
  isGenerating: {
    type: Boolean,
    default: false
  },
  messageContents: {
    type: Map,
    default: () => new Map()
  },
  latexRenderEnabled: {
    type: Boolean,
    default: true
  }
})

defineEmits(['delete-message'])

// 响应式数据
const messagesContainer = ref()
const imageViewerVisible = ref(false)
const imagePreviewList = ref([])
const imagePreviewIndex = ref(0)

// 方法
const openImageViewer = (url) => {
  imagePreviewList.value = [url]
  imagePreviewIndex.value = 0
  imageViewerVisible.value = true
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(() => props.messages.length, () => {
  scrollToBottom()
})

watch(() => props.isGenerating, (newVal) => {
  if (newVal) {
    scrollToBottom()
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToBottom
})
</script>

<style scoped>
.chat-messages {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.message-wrapper.user {
  align-items: flex-end;
}

.message-wrapper.assistant {
  align-items: flex-start;
}

.generating-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-color-light);
  border-radius: var(--border-radius-base);
  color: var(--text-secondary);
  font-size: var(--font-size-small);
  align-self: flex-start;
}

.generating-indicator .el-icon {
  color: var(--primary-color);
}

.loading-content {
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-sm);
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--bg-color-light);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
</style>
