<template>
  <div class="input-area">
    <el-input
      :model-value="inputMessage"
      @update:model-value="$emit('update:inputMessage', $event)"
      type="textarea"
      :rows="3"
      placeholder="输入您的消息..."
      :disabled="isGenerating"
      @keydown.ctrl.enter="$emit('send-message')"
    />
    
    <!-- 待发送的图片预览区 -->
    <div v-if="pendingPreviews.length" class="pending-attachments">
      <div v-for="p in pendingPreviews" :key="p.id" class="pending-thumb">
        <img 
          :src="p.url" 
          :alt="p.name" 
          @click="$emit('open-image-viewer', p.url)" 
        />
        <span class="name">{{ p.name }}</span>
        <el-button 
          size="small" 
          text 
          type="danger" 
          @click="$emit('remove-pending-attachment', p.id)"
        >
          移除
        </el-button>
      </div>
    </div>
    
    <div class="input-actions">
      <!-- 上传进度条 -->
      <div v-if="uploadingImage" class="upload-progress">
        <el-progress 
          :percentage="uploadProgress" 
          :stroke-width="6" 
          :text-inside="true" 
          status="success" 
        />
      </div>
      
      <el-upload
        accept="image/*"
        :show-file-list="false"
        :auto-upload="false"
        @change="$emit('upload-change', $event)"
      >
        <el-button type="default">上传图片</el-button>
      </el-upload>
      
      <el-button
        type="primary"
        :loading="isGenerating"
        :disabled="!inputMessage.trim()"
        @click="$emit('send-message')"
      >
        发送 (Ctrl+Enter)
      </el-button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  inputMessage: {
    type: String,
    default: ''
  },
  isGenerating: {
    type: Boolean,
    default: false
  },
  pendingPreviews: {
    type: Array,
    default: () => []
  },
  uploadingImage: {
    type: Boolean,
    default: false
  },
  uploadProgress: {
    type: Number,
    default: 0
  }
})

defineEmits([
  'update:inputMessage',
  'send-message',
  'upload-change',
  'open-image-viewer',
  'remove-pending-attachment'
])
</script>

<style scoped>
.input-area {
  padding: var(--spacing-md);
  background: var(--bg-color);
  border-top: 1px solid var(--border-light);
}

.pending-attachments {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.pending-thumb {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-base);
  background: var(--bg-color-light);
}

.pending-thumb img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: var(--transition-base);
}

.pending-thumb img:hover {
  transform: scale(1.05);
}

.pending-thumb .name {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  gap: var(--spacing-sm);
}

.upload-progress {
  flex: 1;
  margin-right: var(--spacing-sm);
}

@media (max-width: 768px) {
  .input-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .upload-progress {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }
}
</style>
