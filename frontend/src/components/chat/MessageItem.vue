<template>
  <div class="message-item" :class="messageClasses">
    <div class="message-avatar">
      <div class="avatar-circle" :class="avatarClasses">
        {{ avatarText }}
      </div>
    </div>
    
    <div class="message-content-wrapper">
      <div class="message-header">
        <span class="message-role">{{ roleText }}</span>
        <span v-if="message.role === 'assistant' && temperatureText" class="message-temperature">
          {{ temperatureText }}
        </span>
      </div>

      <div class="message-content">
        <div
          class="message-text"
          v-html="processedContent"
          @click="handleMessageClick"
        ></div>
      </div>

      <!-- 消息底部信息 -->
      <div class="message-footer">
        <span class="message-time">{{ formattedTime }}</span>
        <span v-if="costText" class="message-cost">{{ costText }}</span>
        <el-button
          v-if="showActions"
          :icon="Delete"
          size="small"
          type="danger"
          text
          class="delete-message-btn"
          @click="handleDelete"
          title="删除消息"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { formatTime } from '@/utils/messageUtils.js'
import { useMessageProcessing } from '@/composables/useMessageProcessing.js'

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  messageContents: {
    type: Map,
    default: () => new Map()
  },
  latexRenderEnabled: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['delete-message', 'open-image-viewer'])

// 响应式数据
const messageMetadata = ref(null)

// 计算属性
const messageClasses = computed(() => {
  return [
    'message-item',
    `message-${props.message.role}`,
    {
      'has-actions': props.showActions
    }
  ]
})

const avatarClasses = computed(() => {
  return [
    'avatar-circle',
    `avatar-${props.message.role}`
  ]
})

const avatarText = computed(() => {
  return props.message.role === 'user' ? '我' : 'AI'
})

const roleText = computed(() => {
  if (props.message.role === 'user') {
    return '我'
  } else {
    // 如果是AI消息，尝试使用从MinIO获取的role值（可能是模型名称）
    if (messageMetadata.value && messageMetadata.value.role && messageMetadata.value.role !== 'assistant') {
      return messageMetadata.value.role
    }
    return 'AI助手'
  }
})

const formattedTime = computed(() => {
  return formatTime(props.message.created_at || props.message.timestamp)
})

const temperatureText = computed(() => {
  if (props.message.role === 'assistant' && messageMetadata.value && messageMetadata.value.temperature !== undefined) {
    return `温度: ${messageMetadata.value.temperature}`
  }
  return null
})

const costText = computed(() => {
  if (messageMetadata.value && messageMetadata.value.cost) {
    const cost = messageMetadata.value.cost
    if (cost.total_cost !== undefined) {
      return `费用: $${cost.total_cost.toFixed(6)}`
    }
  }
  return null
})

// 使用消息处理工具
const { processMessage } = useMessageProcessing()

const processedContent = computed(() => {
  // 优先使用缓存的内容（已经处理过的）
  if (props.messageContents.has(props.message.id)) {
    return props.messageContents.get(props.message.id)
  }

  // 如果有content_url，返回加载提示
  if (props.message.content_url) {
    return '<div class="loading-content">正在加载消息内容...</div>'
  }

  // 否则处理原始内容
  const rawContent = props.message.content || ''
  if (!rawContent) return ''

  return processMessage(rawContent, {
    enableLatex: props.latexRenderEnabled,
    enableMarkdown: true
  })
})

// 方法
const handleDelete = () => {
  emit('delete-message', props.message.id)
}

// 处理消息内容中的图片点击
const handleMessageClick = (event) => {
  const img = event.target.closest('img')
  if (!img) return

  const src = img.getAttribute('src')
  if (!src) return

  // 如果是data URL（base64图片），直接使用
  if (src.startsWith('data:')) {
    emit('open-image-viewer', src)
    return
  }

  // 如果是其他URL，通过代理访问
  const proxiedUrl = src.startsWith('/api/v1/content/proxy?')
    ? src
    : `/api/v1/content/proxy?url=${encodeURIComponent(src)}`

  emit('open-image-viewer', proxiedUrl)
}

// 代码复制功能
const copyCodeToClipboard = async (code) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代的Clipboard API
      await navigator.clipboard.writeText(code)
    } else {
      // 降级方案：使用传统的execCommand
      const textArea = document.createElement('textarea')
      textArea.value = code
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    ElMessage.success('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 处理代码复制按钮点击
const handleCodeCopyClick = (event) => {
  if (event.target.closest('.code-copy-btn')) {
    const button = event.target.closest('.code-copy-btn')
    const encodedCode = button.getAttribute('data-code')
    if (encodedCode) {
      try {
        const code = decodeURIComponent(encodedCode)
        copyCodeToClipboard(code)

        // 临时显示"已复制"反馈
        const copyText = button.querySelector('.copy-text')
        if (copyText) {
          const originalText = copyText.textContent
          copyText.textContent = '已复制!'
          setTimeout(() => {
            copyText.textContent = originalText
          }, 2000)
        }
      } catch (err) {
        console.error('解码代码失败:', err)
        ElMessage.error('复制失败')
      }
    }
  }
}

// 获取消息元数据
const fetchMessageMetadata = async () => {
  if (props.message.content_url) {
    try {
      const response = await fetch(props.message.content_url)
      if (response.ok) {
        const data = await response.json()
        messageMetadata.value = data
      }
    } catch (error) {
      console.warn('获取消息元数据失败:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  fetchMessageMetadata()
  // 添加代码复制事件监听器
  document.addEventListener('click', handleCodeCopyClick)
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleCodeCopyClick)
})
</script>

<style scoped>
.message-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  position: relative;
}

.message-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}



.message-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.avatar-circle.avatar-user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar-circle.avatar-assistant {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
  position: relative;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-role {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.message-temperature {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.message-content {
  position: relative;
}

.message-text {
  font-size: 15px;
  line-height: 1.7;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-text :deep(a) {
  color: #409eff;
  text-decoration: none;
}

.message-text :deep(a:hover) {
  text-decoration: underline;
}

.message-text :deep(br) {
  line-height: 1.8;
}

/* Markdown样式增强 */
.message-text :deep(h1) {
  font-size: 1.5em;
  font-weight: 600;
  margin: 16px 0 12px 0;
  color: #2c3e50;
  border-bottom: 2px solid #e1e4e8;
  padding-bottom: 8px;
}

.message-text :deep(h2) {
  font-size: 1.3em;
  font-weight: 600;
  margin: 14px 0 10px 0;
  color: #2c3e50;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 6px;
}

.message-text :deep(h3) {
  font-size: 1.2em;
  font-weight: 600;
  margin: 12px 0 8px 0;
  color: #2c3e50;
}

.message-text :deep(h4),
.message-text :deep(h5),
.message-text :deep(h6) {
  font-size: 1.1em;
  font-weight: 600;
  margin: 10px 0 6px 0;
  color: #2c3e50;
}

.message-text :deep(p) {
  margin: 8px 0;
  line-height: 1.7;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.message-text :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.message-text :deep(blockquote) {
  margin: 12px 0;
  padding: 8px 16px;
  border-left: 4px solid #409eff;
  background: #f8f9fa;
  color: #666;
  font-style: italic;
}

.message-text :deep(strong) {
  font-weight: 600;
  color: #2c3e50;
}

.message-text :deep(em) {
  font-style: italic;
  color: #555;
}

.message-text :deep(table) {
  border-collapse: collapse;
  margin: 12px 0;
  width: 100%;
  border: 1px solid #e1e4e8;
}

.message-text :deep(th),
.message-text :deep(td) {
  border: 1px solid #e1e4e8;
  padding: 8px 12px;
  text-align: left;
}

.message-text :deep(th) {
  background: #f6f8fa;
  font-weight: 600;
}

.message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-cost {
  font-size: 12px;
  color: #666;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.delete-message-btn {
  padding: 4px 6px !important;
  min-height: 24px !important;
  font-size: 12px !important;
}

.delete-message-btn:hover {
  background-color: var(--el-color-danger-light-9) !important;
  color: var(--el-color-danger) !important;
}

/* 用户消息样式 */
.message-user {
  flex-direction: row-reverse;
}

.message-user .message-content-wrapper {
  text-align: right;
}

.message-user .message-header {
  justify-content: flex-end;
}

.message-user .message-text {
  background: #f0f9ff;
  padding: 12px 16px;
  border-radius: 18px 18px 4px 18px;
  display: inline-block;
  max-width: 80%;
  text-align: left;
}

.message-user .message-actions {
  left: 0;
  right: auto;
}

/* AI消息样式 */
.message-assistant .message-text {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 18px 18px 18px 4px;
  border: 1px solid #e9ecef;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .message-item:not(:last-child) {
    border-bottom-color: #333;
  }

  .message-role {
    color: #e6e6e6;
  }

  .message-time {
    color: #999;
  }

  .message-text {
    color: #e6e6e6;
  }

  .message-user .message-text {
    background: #1a365d;
    border-color: #2d3748;
  }

  .message-assistant .message-text {
    background: #2d3748;
    border-color: #4a5568;
  }

  .message-actions {
    background: rgba(45, 55, 72, 0.9);
  }
}

/* 代码块样式 */
.message-text :deep(.code-block) {
  margin: 8px 0;
  border-radius: 6px;
  overflow: hidden;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
}

.message-text :deep(.code-block pre) {
  margin: 0;
  padding: 16px;
  background: transparent;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.message-text :deep(.code-block code) {
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
}

.message-text :deep(.inline-code) {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  border: 1px solid #e1e4e8;
}

/* 数学公式样式 */
.message-text :deep(.math-block) {
  margin: 12px 0;
  text-align: center;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e1e4e8;
}

.message-text :deep(.math-inline) {
  display: inline-block;
  margin: 0 2px;
}

/* 图片样式 */
.message-text :deep(.message-image) {
  max-width: 100%;
  max-height: 400px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  cursor: pointer;
  display: block;
  margin: 8px 0;
  border: 1px solid #e1e4e8;
  transition: transform 0.2s ease;
}

.message-text :deep(.message-image:hover) {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 暗色主题下的代码和数学公式样式 */
.dark .message-text :deep(.code-block) {
  background: #161b22;
  border-color: #30363d;
}

.dark .message-text :deep(.inline-code) {
  background: #161b22;
  border-color: #30363d;
  color: #f0f6fc;
}

.dark .message-text :deep(.math-block) {
  background: #161b22;
  border-color: #30363d;
  color: #f0f6fc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-item {
    gap: 8px;
    padding: 12px 0;
  }

  .avatar-circle {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .message-user .message-text,
  .message-assistant .message-text {
    max-width: 90%;
    padding: 10px 12px;
    font-size: 13px;
  }
}
</style>
