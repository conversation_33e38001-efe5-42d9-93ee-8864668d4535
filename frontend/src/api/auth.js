import { get, post } from './index'

// 认证相关API
export const authAPI = {
  // 用户登录
  login: (apiKey) => {
    return post('/api/v1/auth/login', { api_key: apiKey })
  },

  // 用户登出
  logout: () => {
    return post('/api/v1/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return get('/api/v1/auth/me')
  },

  // 刷新访问令牌
  refreshToken: () => {
    return post('/api/v1/auth/refresh')
  }
}
