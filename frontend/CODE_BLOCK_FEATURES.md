# 代码块功能实现说明

## 功能概述

在InspirFlow聊天界面中实现了增强的代码块显示功能，包括：

1. **美观的代码块样式** - 现代化的设计，支持语言标识
2. **一键复制功能** - 右上角的复制按钮，支持一键复制代码到剪贴板
3. **语言标识显示** - 自动识别并显示代码语言类型
4. **响应式设计** - 支持深色模式和移动端适配
5. **复制反馈** - 复制成功后的视觉反馈

## 实现细节

### 1. 代码块处理函数 (`processMessageContentWithCodeBlocks`)

位置：`frontend/src/views/Chat.vue` 第310-340行

功能：
- 使用正则表达式识别HTML中的 `<pre><code>` 代码块
- 提取语言信息（如果存在）
- 生成包含复制按钮的增强HTML结构
- 使用URL编码安全地存储代码内容

### 2. 复制功能实现

#### 事件监听器设置
- 在组件挂载时添加全局点击事件监听器
- 使用事件委托处理动态生成的复制按钮
- 在组件销毁时清理事件监听器

#### 复制逻辑 (`copyCodeToClipboard`)
- 优先使用现代的 `navigator.clipboard.writeText()` API
- 提供降级方案使用传统的 `document.execCommand('copy')`
- 包含错误处理和用户反馈

### 3. 样式设计

#### 代码块容器
- 圆角边框和阴影效果
- 浅色背景和边框
- 相对定位以支持绝对定位的子元素

#### 语言标签
- 左上角显示编程语言类型
- 半透明背景，小字体，大写显示
- 仅在有语言信息时显示

#### 复制按钮
- 右上角位置，包含图标和文字
- 悬停效果：轻微上移和阴影
- 点击效果：按下动画
- 复制成功后临时显示"已复制!"文字

#### 深色模式支持
- 自动适配系统深色模式偏好
- 深色背景和边框颜色
- 调整文字颜色以保持可读性

## 使用方式

### 在聊天消息中
代码块会自动被识别和增强，用户只需：
1. 查看带有语言标签的代码块
2. 点击右上角的"复制"按钮
3. 代码会被复制到剪贴板，按钮会显示"已复制!"反馈

### 支持的代码格式
```html
<pre><code class="language-javascript">
// JavaScript代码
function hello() {
    console.log("Hello World!");
}
</code></pre>
```

```html
<pre><code class="language-python">
# Python代码
def hello():
    print("Hello World!")
</code></pre>
```

```html
<pre><code>
# 无语言标识的代码
npm install vue
</code></pre>
```

## 技术特点

### 1. 安全性
- 使用URL编码存储代码内容，避免XSS攻击
- 不使用innerHTML直接插入用户内容
- 事件委托避免内联事件处理器

### 2. 兼容性
- 支持现代浏览器的Clipboard API
- 提供传统浏览器的降级方案
- 响应式设计适配各种屏幕尺寸

### 3. 用户体验
- 流畅的动画效果
- 清晰的视觉反馈
- 直观的操作界面
- 无障碍访问支持

### 4. 性能优化
- 事件委托减少内存占用
- 延迟加载避免阻塞渲染
- CSS动画使用transform属性优化性能

## 测试

可以通过访问 `http://localhost:3000/test-codeblock.html` 查看代码块功能的演示页面，包含：
- JavaScript代码示例
- Python代码示例  
- 无语言标签的代码块示例

## 未来改进

1. **语法高亮** - 集成代码语法高亮库
2. **行号显示** - 为长代码块添加行号
3. **代码折叠** - 支持长代码块的折叠/展开
4. **多主题支持** - 提供更多代码块主题选择
5. **快捷键支持** - 支持键盘快捷键复制代码
