# 开发环境 Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装 curl 用于健康检查
RUN apk add --no-cache curl

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 设置构建时环境变量
ARG VITE_API_BASE_URL
ARG VITE_PROXY_TARGET
ARG VITE_WS_URL
ARG VITE_DEV_MODE
ARG VITE_DEBUG_LOG
ARG VITE_ENABLE_LATEX
ARG VITE_ENABLE_DARK_THEME
ARG VITE_DEFAULT_THEME
ARG VITE_DEFAULT_LANGUAGE
ARG VITE_SIDEBAR_COLLAPSED
ARG VITE_DEFAULT_MODEL
ARG VITE_DEFAULT_TEMPERATURE
ARG VITE_MAX_MESSAGE_LENGTH
ARG VITE_MESSAGE_HISTORY_LIMIT
ARG VITE_MAX_FILE_SIZE
ARG VITE_ALLOWED_IMAGE_TYPES
ARG VITE_REQUEST_TIMEOUT
ARG VITE_AUTO_SAVE_INTERVAL

ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_PROXY_TARGET=$VITE_PROXY_TARGET
ENV VITE_WS_URL=$VITE_WS_URL
ENV VITE_DEV_MODE=$VITE_DEV_MODE
ENV VITE_DEBUG_LOG=$VITE_DEBUG_LOG
ENV VITE_ENABLE_LATEX=$VITE_ENABLE_LATEX
ENV VITE_ENABLE_DARK_THEME=$VITE_ENABLE_DARK_THEME
ENV VITE_DEFAULT_THEME=$VITE_DEFAULT_THEME
ENV VITE_DEFAULT_LANGUAGE=$VITE_DEFAULT_LANGUAGE
ENV VITE_SIDEBAR_COLLAPSED=$VITE_SIDEBAR_COLLAPSED
ENV VITE_DEFAULT_MODEL=$VITE_DEFAULT_MODEL
ENV VITE_DEFAULT_TEMPERATURE=$VITE_DEFAULT_TEMPERATURE
ENV VITE_MAX_MESSAGE_LENGTH=$VITE_MAX_MESSAGE_LENGTH
ENV VITE_MESSAGE_HISTORY_LIMIT=$VITE_MESSAGE_HISTORY_LIMIT
ENV VITE_MAX_FILE_SIZE=$VITE_MAX_FILE_SIZE
ENV VITE_ALLOWED_IMAGE_TYPES=$VITE_ALLOWED_IMAGE_TYPES
ENV VITE_REQUEST_TIMEOUT=$VITE_REQUEST_TIMEOUT
ENV VITE_AUTO_SAVE_INTERVAL=$VITE_AUTO_SAVE_INTERVAL

# 暴露端口
EXPOSE 20020

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:20020 || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "20020"]
