# Records API 文档

## 概述

Records API 是一个用户管理和对话记录系统，提供用户认证、对话管理、消息处理和计费功能。

**基础URL**: `http://**************:20002`

## 认证

API 使用 Bearer Token 认证：
```
Authorization: Bearer <api_key>
```

## API 端点分类

### 公共端点 (无需认证)
- `GET /health` - 健康检查
- `GET /api/public/ping` - 服务状态检查

### 用户端点 (需要用户认证)
- `GET /api/v1/user/profile` - 获取用户资料
- `PUT /api/v1/user/profile` - 更新用户资料
- `GET /api/v1/user/billing` - 获取用户计费信息

### 对话管理端点 (需要用户认证)
- `GET /api/v1/user/conversations/` - 获取用户对话列表
- `POST /api/v1/user/conversations/` - 创建新对话
- `GET /api/v1/user/conversations/{conversation_id}` - 获取特定对话
- `PUT /api/v1/user/conversations/{conversation_id}` - 更新对话
- `DELETE /api/v1/user/conversations/{conversation_id}` - 删除对话

### 消息管理端点 (需要用户认证)
- `GET /api/v1/user/messages/` - 获取消息列表
- `POST /api/v1/user/messages/` - 创建新消息
- `GET /api/v1/user/messages/{message_id}` - 获取特定消息
- `PUT /api/v1/user/messages/{message_id}` - 更新消息
- `DELETE /api/v1/user/messages/{message_id}` - 删除消息

### 管理员端点 (需要管理员权限 ≥9)
- `GET /api/v1/admin/users` - 获取所有用户列表
- `POST /api/v1/admin/users` - 创建新用户
- `GET /api/v1/admin/users/{user_id}` - 获取特定用户信息
- `PUT /api/v1/admin/users/{user_id}` - 更新用户信息
- `DELETE /api/v1/admin/users/{user_id}` - 删除用户
- `POST /api/v1/admin/users/{user_id}/recharge` - 用户充值
- `GET /api/v1/admin/users/{user_id}/billing` - 获取用户计费记录
- `GET /api/v1/admin/users/{user_id}/recharge-records` - 获取用户充值记录

## 权限级别

- **1-5**: 普通用户权限
- **6-8**: 高级用户权限  
- **9-10**: 管理员权限

## 分页参数

支持分页的端点使用以下查询参数：
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 10, 最大: 100)

## 响应格式

### 成功响应
```json
{
  "data": "响应数据",
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "detail": "错误描述"
}
```

### 分页响应
```json
{
  "items": [],
  "total": 100,
  "page": 1,
  "size": 10
}
```

## 状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证错误
- `500` - 服务器内部错误

## 数据结构参考

详细的请求和响应数据结构请参考 `record_api_service/schemas.py` 文件，该文件包含所有 Pydantic 模型定义。

## 使用示例

### 获取用户资料
```bash
curl -H "Authorization: Bearer your-api-key" \
     http://**************:20002/api/v1/user/profile
```

### 创建对话
```bash
curl -X POST \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{"title": "新对话"}' \
     http://**************:20002/api/v1/user/conversations/
```

### 管理员获取用户列表
```bash
curl -H "Authorization: Bearer admin-api-key" \
     http://**************:20002/api/v1/admin/users
```

## 开发环境

- **API 文档**: http://**************:20002/docs
- **管理界面**: http://localhost:20003
- **数据库**: MySQL 8.0

## 注意事项

1. 所有时间字段使用 ISO 8601 格式
2. 金额字段使用 Decimal 类型，精确到小数点后4位
3. API Key 需要妥善保管，避免泄露
4. 管理员操作需要权限级别 ≥9
5. 消息内容存储在 MinIO 中，API 返回存储URL
